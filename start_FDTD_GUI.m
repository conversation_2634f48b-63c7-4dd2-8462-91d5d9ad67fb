function start_FDTD_GUI()
%% 二维FDTD声波测井仿真系统启动脚本
% 功能：启动图形化用户界面，用于专利申请和技术演示
% 版本：1.0
% 日期：2025年

    fprintf('=== 二维FDTD声波测井仿真系统 ===\n');
    fprintf('版本：1.0\n');
    fprintf('适用于专利申请和技术演示\n');
    fprintf('=====================================\n\n');
    
    % 检查必要文件
    required_files = {'kuaisu_main.m', 'FDTD_GUI_Main.m'};
    missing_files = {};
    
    for i = 1:length(required_files)
        if ~exist(required_files{i}, 'file')
            missing_files{end+1} = required_files{i};
        end
    end
    
    if ~isempty(missing_files)
        fprintf('❌ 缺少必要文件:\n');
        for i = 1:length(missing_files)
            fprintf('   - %s\n', missing_files{i});
        end
        fprintf('\n请确保所有必要文件都在当前目录中。\n');
        return;
    end
    
    fprintf('✅ 所有必要文件检查完成\n');
    
    % 检查MATLAB版本
    matlab_version = version('-release');
    year = str2double(matlab_version(1:4));
    
    if year < 2018
        warning('建议使用MATLAB 2018a或更高版本以获得最佳体验');
    end
    
    fprintf('✅ MATLAB版本: %s\n', matlab_version);
    
    % 设置路径
    current_path = pwd;
    fprintf('✅ 当前工作目录: %s\n', current_path);
    
    % 清理工作空间（可选）
    answer = questdlg('是否清理当前工作空间？', ...
                     '工作空间清理', 'Yes', 'No', 'No');
    
    if strcmp(answer, 'Yes')
        evalin('base', 'clear all');
        fprintf('✅ 工作空间已清理\n');
    end
    
    % 启动GUI
    fprintf('\n🚀 正在启动图形界面...\n');
    
    try
        FDTD_GUI_Main();
        fprintf('✅ 图形界面启动成功！\n\n');
        
        % 显示使用提示
        fprintf('=== 使用提示 ===\n');
        fprintf('1. 在左侧面板调整仿真参数\n');
        fprintf('2. 点击"开始仿真"运行计算\n');
        fprintf('3. 在右侧选项卡中查看结果\n');
        fprintf('4. 使用"参数对比"功能分析不同参数的影响\n');
        fprintf('================\n\n');
        
        fprintf('💡 专利申请建议：\n');
        fprintf('- 重点展示参数调节的实时效果\n');
        fprintf('- 保存不同参数组合的对比结果\n');
        fprintf('- 记录关键技术参数的优化过程\n');
        fprintf('- 生成专业的技术图表和数据\n\n');
        
    catch ME
        fprintf('❌ 启动失败: %s\n', ME.message);
        fprintf('\n可能的解决方案:\n');
        fprintf('1. 检查MATLAB版本是否支持所需功能\n');
        fprintf('2. 确保所有文件完整且未损坏\n');
        fprintf('3. 重启MATLAB后重试\n');
        fprintf('4. 检查系统内存是否充足\n');
    end
end

%% 系统检查函数
function checkSystemRequirements()
    %% 检查系统要求
    
    fprintf('\n=== 系统要求检查 ===\n');
    
    % 检查内存
    if ispc
        [~, sys_info] = memory;
        total_mem_gb = sys_info.PhysicalMemory.Total / 1024^3;
        fprintf('系统内存: %.1f GB\n', total_mem_gb);
        
        if total_mem_gb < 4
            warning('建议系统内存至少4GB以确保流畅运行');
        end
    end
    
    % 检查工具箱
    required_toolboxes = {'Signal Processing Toolbox'};
    
    for i = 1:length(required_toolboxes)
        if license('test', strrep(required_toolboxes{i}, ' ', '_'))
            fprintf('✅ %s 可用\n', required_toolboxes{i});
        else
            fprintf('⚠️  %s 不可用（某些功能可能受限）\n', required_toolboxes{i});
        end
    end
    
    fprintf('==================\n');
end

%% 演示数据生成函数
function generateDemoData()
    %% 生成演示数据
    
    fprintf('\n=== 生成演示数据 ===\n');
    
    answer = questdlg('是否生成演示数据？这将运行一个快速仿真示例。', ...
                     '演示数据', 'Yes', 'No', 'No');
    
    if strcmp(answer, 'Yes')
        try
            fprintf('正在生成演示数据...\n');
            
            % 设置演示参数
            assignin('base', 'shot_start', 30);
            assignin('base', 'shot_end', 32);
            assignin('base', 'maxt', 1000);
            assignin('base', 'use_direct_indexing', true);
            
            % 运行快速仿真
            evalin('base', 'kuaisu_main');
            
            fprintf('✅ 演示数据生成完成\n');
            fprintf('可以在GUI中加载并查看结果\n');
            
        catch ME
            fprintf('❌ 演示数据生成失败: %s\n', ME.message);
        end
    end
    
    fprintf('==================\n');
end

%% 帮助信息函数
function showHelpInfo()
    %% 显示帮助信息
    
    fprintf('\n=== 帮助信息 ===\n');
    fprintf('主要功能:\n');
    fprintf('1. 参数设置 - 调整物理和计算参数\n');
    fprintf('2. 实时监控 - 查看仿真进度和波场\n');
    fprintf('3. 结果分析 - 多种方式显示和分析结果\n');
    fprintf('4. 参数对比 - 对比不同参数的影响\n\n');
    
    fprintf('快捷操作:\n');
    fprintf('- Ctrl+S: 保存当前参数\n');
    fprintf('- Ctrl+L: 加载参数文件\n');
    fprintf('- Ctrl+R: 重置参数\n');
    fprintf('- F5: 刷新结果文件列表\n\n');
    
    fprintf('技术支持:\n');
    fprintf('- 查看README文档了解详细说明\n');
    fprintf('- 检查MATLAB命令窗口的错误信息\n');
    fprintf('- 确保输入参数在合理范围内\n');
    fprintf('================\n');
end

% 如果直接运行此脚本，则启动GUI
if ~nargout
    % 显示欢迎信息
    clc;
    fprintf('欢迎使用二维FDTD声波测井仿真系统！\n\n');
    
    % 检查系统要求
    checkSystemRequirements();
    
    % 显示帮助信息
    showHelpInfo();
    
    % 询问是否生成演示数据
    generateDemoData();
    
    % 启动主程序
    start_FDTD_GUI();
end
