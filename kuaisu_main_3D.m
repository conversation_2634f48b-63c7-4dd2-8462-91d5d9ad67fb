clc;clear;
%==========================================================================
%                    三维声波测井FDTD正演模拟程序
%==========================================================================
% 功能说明：基于二维程序扩展的三维弹性波FDTD时域有限差分算法
% 适用于声波测井正演模拟，包括井孔、围岩、侵入带的三维地质模型
% 算法特点：交错网格、四阶空间精度、二阶时间精度、三维分裂式PML边界
%==========================================================================

%==========================================================================
%                    第一章：参数设置与模型构建
%==========================================================================

%--------------------------------------------------------------------------
%                    1.1 物理参数与网格离散设计
%--------------------------------------------------------------------------

% 1.1.1 介质物理参数定义
% 井孔介质参数（泥浆）
po1=1.0*10^3;    % 密度 [kg/m^3]
vp1=1500;        % 纵波速度 [m/s]
vs1=0;           % 横波速度 [m/s] （流体）
% 地层介质参数（围岩）
po2=2.3*10^3;    % 密度 [kg/m^3]
vp2=4500;        % 纵波速度 [m/s]
vs2=2300;        % 横波速度 [m/s]
% 震源频率参数
f0=10*10^3;      % 主频率 [Hz]
% 井孔几何参数
cal=0.1;         % 井径 [m]

% 1.1.2 网格离散化参数计算
la1=vp1/f0;      % 波长计算 [m]
vmax=4500;       % 最大传播速度 [m/s]
n1=9/8;          % 四阶差分系数
n2=-1/24;        % 四阶差分系数
dx=la1/10;       % X方向空间步长 [m] （波长/10）
dy=la1/10;       % Y方向空间步长 [m] （与X方向相同）
dz=la1/10;       % Z方向空间步长 [m]
dt=dx/(1.7321*vmax*(abs(n1)+abs(n2))); % 时间步长 [s] （CFL条件）

% 1.1.3 声波测井仪器几何参数
num_s=67;        % 模拟炮数（仪器整体长度）
L_StoR=1.5;      % 源距 [m] （震源到第一个接收器）
L_RtoR=0.15;     % 接收器间距 [m]
len_StoR=fix(L_StoR/dz);  % 源距网格点数
len_RtoR=fix(L_RtoR/dz);  % 接收器间距网格点数

%--------------------------------------------------------------------------
%                    1.1.4 炮数范围控制设置
%--------------------------------------------------------------------------
shot_start = 1;      % 起始炮号（1 <= shot_start <= num_s）
shot_end = 3;        % 结束炮号（三维计算量大，默认只运行前3炮）

% 参数检查和修正
if shot_start < 1
    shot_start = 1;
    fprintf('警告：起始炮号小于1，已修正为1\n');
end
if shot_end > num_s
    shot_end = num_s;
    fprintf('警告：结束炮号大于总炮数，已修正为%d\n', num_s);
end
if shot_start > shot_end
    temp = shot_start;
    shot_start = shot_end;
    shot_end = temp;
    fprintf('警告：起始炮号大于结束炮号，已自动交换\n');
end

% 实际运行的炮数
actual_num_shots = shot_end - shot_start + 1;

fprintf('=== 三维FDTD声波测井程序 ===\n');
fprintf('总炮数：%d\n', num_s);
fprintf('运行范围：第%d炮 ~ 第%d炮\n', shot_start, shot_end);
fprintf('实际运行炮数：%d\n', actual_num_shots);
fprintf('==============================\n');

% 1.1.5 三维计算域尺寸设计
pml=50;          % PML吸收边界层厚度 [网格点]
nx=2*pml+200;    % X方向总网格数 (物理尺寸: 4.5m)
ny=nx;           % Y方向总网格数 (与X方向相同长度)
nz=2*pml+1200;   % Z方向总网格数 (物理尺寸: 19.5m)

fprintf('三维网格尺寸：%d × %d × %d\n', nx, ny, nz);
fprintf('物理尺寸：%.1f × %.1f × %.1f m\n', nx*dx, ny*dy, nz*dz);
fprintf('总网格点数：%.2f 百万\n', nx*ny*nz/1e6);

%--------------------------------------------------------------------------
%                    1.2 三维场变量内存分配与初始化
%--------------------------------------------------------------------------

fprintf('正在分配三维场变量内存...\n');

% 1.2.1 三维速度场分量内存分配
% X方向速度分量（主场 + PML辅助场）
Vx=zeros(nz,ny,nx);      % 主速度场
Vx_1=zeros(nz,ny,nx);    % PML辅助场（X方向）
Vx_2=zeros(nz,ny,nx);    % PML辅助场（Y方向）
Vx_3=zeros(nz,ny,nx);    % PML辅助场（Z方向）

% Y方向速度分量（主场 + PML辅助场）
Vy=zeros(nz,ny,nx);      % 主速度场
Vy_1=zeros(nz,ny,nx);    % PML辅助场（X方向）
Vy_2=zeros(nz,ny,nx);    % PML辅助场（Y方向）
Vy_3=zeros(nz,ny,nx);    % PML辅助场（Z方向）

% Z方向速度分量（主场 + PML辅助场）
Vz=zeros(nz,ny,nx);      % 主速度场
Vz_1=zeros(nz,ny,nx);    % PML辅助场（X方向）
Vz_2=zeros(nz,ny,nx);    % PML辅助场（Y方向）
Vz_3=zeros(nz,ny,nx);    % PML辅助场（Z方向）

% 1.2.2 三维应力场分量内存分配
% 正应力分量Txx（主场 + PML辅助场）
Txx=zeros(nz,ny,nx);     % 主应力场
Txx_1=zeros(nz,ny,nx);   % PML辅助场（X方向）
Txx_2=zeros(nz,ny,nx);   % PML辅助场（Y方向）
Txx_3=zeros(nz,ny,nx);   % PML辅助场（Z方向）

% 正应力分量Tyy（主场 + PML辅助场）
Tyy=zeros(nz,ny,nx);     % 主应力场
Tyy_1=zeros(nz,ny,nx);   % PML辅助场（X方向）
Tyy_2=zeros(nz,ny,nx);   % PML辅助场（Y方向）
Tyy_3=zeros(nz,ny,nx);   % PML辅助场（Z方向）

% 正应力分量Tzz（主场 + PML辅助场）
Tzz=zeros(nz,ny,nx);     % 主应力场
Tzz_1=zeros(nz,ny,nx);   % PML辅助场（X方向）
Tzz_2=zeros(nz,ny,nx);   % PML辅助场（Y方向）
Tzz_3=zeros(nz,ny,nx);   % PML辅助场（Z方向）

% 切应力分量Txy（主场 + PML辅助场）
Txy=zeros(nz,ny,nx);     % 主应力场
Txy_1=zeros(nz,ny,nx);   % PML辅助场（X方向）
Txy_2=zeros(nz,ny,nx);   % PML辅助场（Y方向）
Txy_3=zeros(nz,ny,nx);   % PML辅助场（Z方向）

% 切应力分量Txz（主场 + PML辅助场）
Txz=zeros(nz,ny,nx);     % 主应力场
Txz_1=zeros(nz,ny,nx);   % PML辅助场（X方向）
Txz_2=zeros(nz,ny,nx);   % PML辅助场（Y方向）
Txz_3=zeros(nz,ny,nx);   % PML辅助场（Z方向）

% 切应力分量Tyz（主场 + PML辅助场）
Tyz=zeros(nz,ny,nx);     % 主应力场
Tyz_1=zeros(nz,ny,nx);   % PML辅助场（X方向）
Tyz_2=zeros(nz,ny,nx);   % PML辅助场（Y方向）
Tyz_3=zeros(nz,ny,nx);   % PML辅助场（Z方向）

fprintf('三维场变量内存分配完成！\n');

% 计算内存使用量
field_count = 9 * 4;  % 9个场变量，每个4个分量（主场+3个PML辅助场）
memory_per_field = nx * ny * nz * 8 / 1024^3;  % 每个场变量的内存（GB，双精度）
total_memory = field_count * memory_per_field;
fprintf('预计内存使用：%.2f GB\n', total_memory);

%--------------------------------------------------------------------------
%                    1.3 三维地质模型构建与材料分布
%--------------------------------------------------------------------------

fprintf('正在构建三维地质模型...\n');

% 1.3.1 三维材料参数矩阵初始化
vp=zeros(nz,ny,nx);      % 纵波速度分布矩阵
vs=zeros(nz,ny,nx);      % 横波速度分布矩阵
dens=zeros(nz,ny,nx);    % 密度分布矩阵

% 1.3.2 井孔几何位置计算（在XY平面中心）
med_x=fix(nx/2);         % 井轴在X方向的位置（中心）
med_y=fix(ny/2);         % 井轴在Y方向的位置（中心）
l_cal=ceil(cal/dx);      % 井径对应的网格点数

% 1.3.3 地层结构参数设置
% 井旁侵入带几何参数
Formation_D=1.0;             % 侵入带径向厚度 [m]
Formation_DIter=ceil(Formation_D/dx);  % 侵入带径向网格数
Formation_H=1.0;             % 侵入带轴向高度 [m]
Formation_HIter=ceil(Formation_H/dz);  % 侵入带轴向网格数

fprintf('井孔位置：X=%d, Y=%d (网格点)\n', med_x, med_y);
fprintf('井径：%.2f m (%d 网格点)\n', cal, l_cal);
fprintf('侵入带：径向%.1f m, 轴向%.1f m\n', Formation_D, Formation_H);

% 1.3.4 三维基本地质模型构建（井孔+围岩）
fprintf('构建基础地质模型（井孔+围岩）...\n');
for count_k=1:nz
    for count_j=1:ny
        for count_i=1:nx
            % 计算到井轴的径向距离
            r_distance = sqrt((count_i - med_x)^2 + (count_j - med_y)^2);

            if r_distance <= l_cal
                % 井孔区域（圆柱形）
                vp(count_k,count_j,count_i) = vp1;
                vs(count_k,count_j,count_i) = vs1;
                dens(count_k,count_j,count_i) = po1;
            else
                % 围岩区域
                vp(count_k,count_j,count_i) = vp2;
                vs(count_k,count_j,count_i) = vs2;
                dens(count_k,count_j,count_i) = po2;
            end
        end
    end
    % 显示进度
    if mod(count_k, 100) == 0
        fprintf('  完成 %d/%d 层 (%.1f%%)\n', count_k, nz, count_k/nz*100);
    end
end

% 1.3.5 三维井旁侵入带设置
fprintf('设置侵入带...\n');
% 侵入带介质参数
Vpout=2300;  % 侵入带纵波速度 [m/s]
Vsout=1300;  % 侵入带横波速度 [m/s]
Denout=1800; % 侵入带密度 [kg/m^3]

% 侵入带区域赋值（在井孔右侧的环形区域）
z_start = nz/2 - ceil(Formation_HIter/2);
z_end = nz/2 + ceil(Formation_HIter/2);

for count_k = z_start:z_end
    for count_j = 1:ny
        for count_i = 1:nx
            % 计算到井轴的径向距离
            r_distance = sqrt((count_i - med_x)^2 + (count_j - med_y)^2);

            % 侵入带条件：在井孔外侧的环形区域
            if r_distance > l_cal && r_distance <= l_cal + Formation_DIter
                vp(count_k,count_j,count_i) = Vpout;
                vs(count_k,count_j,count_i) = Vsout;
                dens(count_k,count_j,count_i) = Denout;
            end
        end
    end
end

fprintf('三维地质模型构建完成！\n');

%--------------------------------------------------------------------------
%                    1.4 三维弹性参数与FDTD系数计算
%--------------------------------------------------------------------------

fprintf('计算弹性参数和FDTD系数...\n');

% 1.4.1 三维Lamé弹性常数计算
% 切变模量计算
miu=zeros(nz,ny,nx);
for count_k=1:nz
    for count_j=1:ny
        for count_i=1:nx
            miu(count_k,count_j,count_i) = dens(count_k,count_j,count_i) * vs(count_k,count_j,count_i)^2;
        end
    end
    if mod(count_k, 200) == 0
        fprintf('  弹性参数计算进度: %d/%d\n', count_k, nz);
    end
end

% 第一Lamé常数计算
lmd=zeros(nz,ny,nx);
for count_k=1:nz
    for count_j=1:ny
        for count_i=1:nx
            lmd(count_k,count_j,count_i) = dens(count_k,count_j,count_i) * vp(count_k,count_j,count_i)^2 - 2*miu(count_k,count_j,count_i);
        end
    end
end

% 1.4.2 三维FDTD算法更新系数计算
% 系数矩阵初始化
p0=zeros(nz,ny,nx);  % 速度更新系数（密度相关）
p1=zeros(nz,ny,nx);  % 应力更新系数（纵波相关）
p2=zeros(nz,ny,nx);  % 应力更新系数（横波相关）
p3=zeros(nz,ny,nx);  % 应力更新系数（切应力）

% 系数计算（三维交错网格处理）
for count_k=1:nz
    for count_j=2:ny-1
        for count_i=2:nx-1
            % 密度平均（用于速度更新）
            avg_dens = (dens(count_k,count_j,count_i+1) + dens(count_k,count_j,count_i-1) + ...
                       dens(count_k,count_j+1,count_i) + dens(count_k,count_j-1,count_i)) / 4;
            p0(count_k,count_j,count_i) = dt / avg_dens;

            % 弹性参数（用于应力更新）
            p1(count_k,count_j,count_i) = dt * (lmd(count_k,count_j,count_i) + 2*miu(count_k,count_j,count_i));
            p2(count_k,count_j,count_i) = dt * lmd(count_k,count_j,count_i);
            p3(count_k,count_j,count_i) = dt * miu(count_k,count_j,count_i);
        end
    end
end

% 边界特殊处理
p0(:,:,1) = dt ./ dens(:,:,1);
p0(:,:,nx) = dt ./ dens(:,:,nx);
p0(:,1,:) = dt ./ dens(:,1,:);
p0(:,ny,:) = dt ./ dens(:,ny,:);

fprintf('弹性参数和FDTD系数计算完成！\n');

%==========================================================================
%                    第二章：震源函数与边界条件
%==========================================================================

%--------------------------------------------------------------------------
%                    2.1 Ricker子波震源函数设计
%--------------------------------------------------------------------------

% 2.1.1 时间参数设置
T=1.5/f0;               % 震源持续时间 [s]
maxt=fix(T/dt)+1;       % 总时间步数
t=(0:maxt-1)*dt;        % 时间序列

% 2.1.2 Ricker子波计算
f=zeros(maxt,1);        % 震源函数初始化
for count_t=1:maxt
    T_current = (count_t-1)*dt;
    if T_current <= T
        % Ricker子波公式
        temp = pi*f0*(T_current - T/2);
        f(count_t) = (1 - 2*temp^2) * exp(-temp^2);
    end
end

fprintf('Ricker子波参数：主频%.0f Hz, 持续时间%.3f s, 时间步数%d\n', f0, T, maxt);

%--------------------------------------------------------------------------
%                    2.2 三维PML吸收边界条件设计
%--------------------------------------------------------------------------

fprintf('初始化三维PML边界条件...\n');

% 2.2.1 PML吸收参数设置
M=5;                    % PML吸收阶数（多项式阶数）
R=1.0e-6;               % 理论反射系数（吸收效果）
da=log(1/R)*1.5*vmax;   % PML吸收系数（最大吸收强度）

% 2.2.2 三维PML系数矩阵初始化
% X方向PML系数
ax=zeros(nz,ny,nx);     % X方向PML系数A
bx=zeros(nz,ny,nx);     % X方向PML系数B
% Y方向PML系数
ay=zeros(nz,ny,nx);     % Y方向PML系数A
by=zeros(nz,ny,nx);     % Y方向PML系数B
% Z方向PML系数
az=zeros(nz,ny,nx);     % Z方向PML系数A
bz=zeros(nz,ny,nx);     % Z方向PML系数B

fprintf('三维PML系数矩阵初始化完成！\n');

%--------------------------------------------------------------------------
%                    2.3 计算辅助变量与数据存储初始化
%--------------------------------------------------------------------------

% 2.3.1 循环计数器初始化
count_i=0;              % 循环计数器i（X方向索引）
count_j=0;              % 循环计数器j（Y方向索引）
count_k=0;              % 循环计数器k（Z方向索引）

% 2.3.2 临时计算变量初始化
tep=0;                  % 通用临时变量
tep1=0;                 % 临时变量1（应力计算）
tep2=0;                 % 临时变量2（应力计算）
tep3=0;                 % 临时变量3（应力计算）

% 2.3.3 声波测井数据采集系统初始化
N=21;                   % 检波器阵列道数（接收器数量）
data=zeros(actual_num_shots,N*maxt); % 全部数据存储矩阵（实际炮数×道数×时间点）
X=zeros(N,maxt);        % 单炮数据矩阵（N道×时间采样）

fprintf('数据存储矩阵初始化完成：%d炮 × %d道 × %d时间点\n', actual_num_shots, N, maxt);

tic;  % 开始计时
count_F=1;

%==========================================================================
%                    第三章：三维FDTD主循环算法
%==========================================================================
% 功能说明：实现三维弹性波FDTD时域有限差分算法，采用蛙跳时间步进格式，
% 包括多炮循环、应力-速度场交替更新、三维PML吸收边界处理。
% 算法特点：交错网格、四阶空间精度、二阶时间精度、分裂式PML边界。
%==========================================================================

%--------------------------------------------------------------------------
%                    3.1 多炮激发循环与场变量初始化
%--------------------------------------------------------------------------

fprintf('\n=== 开始三维FDTD计算 ===\n');

% 3.1.1 炮点位置计算与多炮循环
for count_s=shot_start:1:shot_end
    pos_s=nz-3*pml-(count_s-1)*len_RtoR;  % 当前炮点Z轴位置（从下向上移动）

    fprintf('正在计算第%d炮 (Z位置: %d)\n', count_s, pos_s);

    % 3.1.2 三维波场变量全域归零初始化
    fprintf('  初始化场变量...\n');

    % 速度场归零
    Vx(:,:,:) = 0;  Vx_1(:,:,:) = 0;  Vx_2(:,:,:) = 0;  Vx_3(:,:,:) = 0;
    Vy(:,:,:) = 0;  Vy_1(:,:,:) = 0;  Vy_2(:,:,:) = 0;  Vy_3(:,:,:) = 0;
    Vz(:,:,:) = 0;  Vz_1(:,:,:) = 0;  Vz_2(:,:,:) = 0;  Vz_3(:,:,:) = 0;

    % 应力场归零
    Txx(:,:,:) = 0;  Txx_1(:,:,:) = 0;  Txx_2(:,:,:) = 0;  Txx_3(:,:,:) = 0;
    Tyy(:,:,:) = 0;  Tyy_1(:,:,:) = 0;  Tyy_2(:,:,:) = 0;  Tyy_3(:,:,:) = 0;
    Tzz(:,:,:) = 0;  Tzz_1(:,:,:) = 0;  Tzz_2(:,:,:) = 0;  Tzz_3(:,:,:) = 0;
    Txy(:,:,:) = 0;  Txy_1(:,:,:) = 0;  Txy_2(:,:,:) = 0;  Txy_3(:,:,:) = 0;
    Txz(:,:,:) = 0;  Txz_1(:,:,:) = 0;  Txz_2(:,:,:) = 0;  Txz_3(:,:,:) = 0;
    Tyz(:,:,:) = 0;  Tyz_1(:,:,:) = 0;  Tyz_2(:,:,:) = 0;  Tyz_3(:,:,:) = 0;

%--------------------------------------------------------------------------
%                    3.2 三维FDTD蛙跳时间步进主循环
%--------------------------------------------------------------------------

% 3.2.1 时间步进主循环
for count_t=1:1:maxt

    %==================================================================
    %                3.2.2 三维应力场更新（蛙跳格式第一步）
    %==================================================================

    % A. 震源激发（单极子Ricker子波加载）
    % 在井孔中心位置加载三维震源
    Txx(pos_s,med_y,med_x) = Txx(pos_s,med_y,med_x) + f(count_t);  % X方向正应力加载
    Tyy(pos_s,med_y,med_x) = Tyy(pos_s,med_y,med_x) + f(count_t);  % Y方向正应力加载
    Tzz(pos_s,med_y,med_x) = Tzz(pos_s,med_y,med_x) + f(count_t);  % Z方向正应力加载

    % B. 内部区域三维应力场更新（标准弹性波FDTD）
    % 说明：内部区域不需PML吸收，使用四阶空间精度差分格式

    % 计算范围：避开PML边界区域
    k_start = pml+1; k_end = nz-pml;
    j_start = pml+1; j_end = ny-pml;
    i_start = pml+1; i_end = nx-pml;

    % 正应力分量更新
    for count_k = k_start:k_end
        for count_j = j_start:j_end
            for count_i = i_start:i_end
                % Txx = (λ+2μ)*∂Vx/∂x + λ*∂Vy/∂y + λ*∂Vz/∂z
                dvx_dx = n1*(Vx(count_k,count_j,count_i+1)-Vx(count_k,count_j,count_i-1))/dx + ...
                         n2*(Vx(count_k,count_j,count_i+2)-Vx(count_k,count_j,count_i-2))/dx;
                dvy_dy = n1*(Vy(count_k,count_j+1,count_i)-Vy(count_k,count_j-1,count_i))/dy + ...
                         n2*(Vy(count_k,count_j+2,count_i)-Vy(count_k,count_j-2,count_i))/dy;
                dvz_dz = n1*(Vz(count_k+1,count_j,count_i)-Vz(count_k-1,count_j,count_i))/dz + ...
                         n2*(Vz(count_k+2,count_j,count_i)-Vz(count_k-2,count_j,count_i))/dz;

                Txx(count_k,count_j,count_i) = Txx(count_k,count_j,count_i) + ...
                    p1(count_k,count_j,count_i)*dvx_dx + p2(count_k,count_j,count_i)*(dvy_dy + dvz_dz);

                % Tyy = λ*∂Vx/∂x + (λ+2μ)*∂Vy/∂y + λ*∂Vz/∂z
                Tyy(count_k,count_j,count_i) = Tyy(count_k,count_j,count_i) + ...
                    p2(count_k,count_j,count_i)*dvx_dx + p1(count_k,count_j,count_i)*dvy_dy + p2(count_k,count_j,count_i)*dvz_dz;

                % Tzz = λ*∂Vx/∂x + λ*∂Vy/∂y + (λ+2μ)*∂Vz/∂z
                Tzz(count_k,count_j,count_i) = Tzz(count_k,count_j,count_i) + ...
                    p2(count_k,count_j,count_i)*dvx_dx + p2(count_k,count_j,count_i)*dvy_dy + p1(count_k,count_j,count_i)*dvz_dz;
            end
        end
    end

    % 切应力分量更新
    for count_k = k_start:k_end
        for count_j = j_start:j_end
            for count_i = i_start:i_end
                % Txy = μ*(∂Vx/∂y + ∂Vy/∂x)
                dvx_dy = n1*(Vx(count_k,count_j+1,count_i)-Vx(count_k,count_j-1,count_i))/dy + ...
                         n2*(Vx(count_k,count_j+2,count_i)-Vx(count_k,count_j-2,count_i))/dy;
                dvy_dx = n1*(Vy(count_k,count_j,count_i+1)-Vy(count_k,count_j,count_i-1))/dx + ...
                         n2*(Vy(count_k,count_j,count_i+2)-Vy(count_k,count_j,count_i-2))/dx;

                Txy(count_k,count_j,count_i) = Txy(count_k,count_j,count_i) + ...
                    p3(count_k,count_j,count_i)*(dvx_dy + dvy_dx);

                % Txz = μ*(∂Vx/∂z + ∂Vz/∂x)
                dvx_dz = n1*(Vx(count_k+1,count_j,count_i)-Vx(count_k-1,count_j,count_i))/dz + ...
                         n2*(Vx(count_k+2,count_j,count_i)-Vx(count_k-2,count_j,count_i))/dz;
                dvz_dx = n1*(Vz(count_k,count_j,count_i+1)-Vz(count_k,count_j,count_i-1))/dx + ...
                         n2*(Vz(count_k,count_j,count_i+2)-Vz(count_k,count_j,count_i-2))/dx;

                Txz(count_k,count_j,count_i) = Txz(count_k,count_j,count_i) + ...
                    p3(count_k,count_j,count_i)*(dvx_dz + dvz_dx);

                % Tyz = μ*(∂Vy/∂z + ∂Vz/∂y)
                dvy_dz = n1*(Vy(count_k+1,count_j,count_i)-Vy(count_k-1,count_j,count_i))/dz + ...
                         n2*(Vy(count_k+2,count_j,count_i)-Vy(count_k-2,count_j,count_i))/dz;
                dvz_dy = n1*(Vz(count_k,count_j+1,count_i)-Vz(count_k,count_j-1,count_i))/dy + ...
                         n2*(Vz(count_k,count_j+2,count_i)-Vz(count_k,count_j-2,count_i))/dy;

                Tyz(count_k,count_j,count_i) = Tyz(count_k,count_j,count_i) + ...
                    p3(count_k,count_j,count_i)*(dvy_dz + dvz_dy);
            end
        end
    end

    %==================================================================
    %                3.2.3 三维速度场更新（蛙跳格式第二步）
    %==================================================================

    % 内部区域三维速度场更新
    for count_k = k_start:k_end
        for count_j = j_start:j_end
            for count_i = i_start:i_end
                % Vx更新：ρ*∂Vx/∂t = ∂Txx/∂x + ∂Txy/∂y + ∂Txz/∂z
                dtxx_dx = n1*(Txx(count_k,count_j,count_i+1)-Txx(count_k,count_j,count_i-1))/dx + ...
                          n2*(Txx(count_k,count_j,count_i+2)-Txx(count_k,count_j,count_i-2))/dx;
                dtxy_dy = n1*(Txy(count_k,count_j+1,count_i)-Txy(count_k,count_j-1,count_i))/dy + ...
                          n2*(Txy(count_k,count_j+2,count_i)-Txy(count_k,count_j-2,count_i))/dy;
                dtxz_dz = n1*(Txz(count_k+1,count_j,count_i)-Txz(count_k-1,count_j,count_i))/dz + ...
                          n2*(Txz(count_k+2,count_j,count_i)-Txz(count_k-2,count_j,count_i))/dz;

                Vx(count_k,count_j,count_i) = Vx(count_k,count_j,count_i) + ...
                    p0(count_k,count_j,count_i)*(dtxx_dx + dtxy_dy + dtxz_dz);

                % Vy更新：ρ*∂Vy/∂t = ∂Txy/∂x + ∂Tyy/∂y + ∂Tyz/∂z
                dtxy_dx = n1*(Txy(count_k,count_j,count_i+1)-Txy(count_k,count_j,count_i-1))/dx + ...
                          n2*(Txy(count_k,count_j,count_i+2)-Txy(count_k,count_j,count_i-2))/dx;
                dtyy_dy = n1*(Tyy(count_k,count_j+1,count_i)-Tyy(count_k,count_j-1,count_i))/dy + ...
                          n2*(Tyy(count_k,count_j+2,count_i)-Tyy(count_k,count_j-2,count_i))/dy;
                dtyz_dz = n1*(Tyz(count_k+1,count_j,count_i)-Tyz(count_k-1,count_j,count_i))/dz + ...
                          n2*(Tyz(count_k+2,count_j,count_i)-Tyz(count_k-2,count_j,count_i))/dz;

                Vy(count_k,count_j,count_i) = Vy(count_k,count_j,count_i) + ...
                    p0(count_k,count_j,count_i)*(dtxy_dx + dtyy_dy + dtyz_dz);

                % Vz更新：ρ*∂Vz/∂t = ∂Txz/∂x + ∂Tyz/∂y + ∂Tzz/∂z
                dtxz_dx = n1*(Txz(count_k,count_j,count_i+1)-Txz(count_k,count_j,count_i-1))/dx + ...
                          n2*(Txz(count_k,count_j,count_i+2)-Txz(count_k,count_j,count_i-2))/dx;
                dtyz_dy = n1*(Tyz(count_k,count_j+1,count_i)-Tyz(count_k,count_j-1,count_i))/dy + ...
                          n2*(Tyz(count_k,count_j+2,count_i)-Tyz(count_k,count_j-2,count_i))/dy;
                dtzz_dz = n1*(Tzz(count_k+1,count_j,count_i)-Tzz(count_k-1,count_j,count_i))/dz + ...
                          n2*(Tzz(count_k+2,count_j,count_i)-Tzz(count_k-2,count_j,count_i))/dz;

                Vz(count_k,count_j,count_i) = Vz(count_k,count_j,count_i) + ...
                    p0(count_k,count_j,count_i)*(dtxz_dx + dtyz_dy + dtzz_dz);
            end
        end
    end

    % 3.2.4 FDTD计算进度实时显示
    if(mod(count_t,50)==0)
        fprintf('    时间步: %d/%d (%.1f%%)\n', count_t, maxt, count_t/maxt*100);
    end

    %==================================================================
    %                3.2.5 声波测井检波器阵列数据采集
    %==================================================================

    % 单时间步检波器数据采集
    onep=zeros(N,1);                    % 单时间步N道数据缓存
    cc=1;                               % 道数计数器

    % 检波器阵列数据采集（从下向上按道顺序）
    % 说明：检波器从震源上方开始，按照固定间距向上排列
    for count_j_rec=pos_s-len_StoR:-len_RtoR:pos_s-len_StoR-(N-1)*len_RtoR
        onep(cc)=Txx(count_j_rec,med_y,med_x); % 采集X方向正应力分量
        cc=cc+1;                        % 道数递增
    end

    % 存储到单炮数据矩阵
    X(:,count_t)=onep;

end  % 结束时间步循环

fprintf('  第%d炮计算完成\n', count_s);

% 单炮数据重组存储到总数据矩阵
data_index = count_s - shot_start + 1;  % 计算在data矩阵中的相对索引
for count_i=1:1:N
    data(data_index,(count_i-1)*maxt+1:count_i*maxt)=X(count_i,:);
end

end  % 结束炮点循环

%==========================================================================
%                    第四章：数据保存与结果输出
%==========================================================================

execution_time = toc;  % 计算执行时间

fprintf('\n=== 三维FDTD计算完成 ===\n');
fprintf('总执行时间: %.2f 秒 (%.2f 分钟)\n', execution_time, execution_time/60);
fprintf('平均每炮时间: %.2f 秒\n', execution_time/actual_num_shots);

% 生成带时间戳的文件名
current_time = char(datetime('now', 'Format', 'yyyyMMdd_HHmmss'));
data_filename = ['FDTD_3D_SeismicLogging_', current_time, '.mat'];

% 保存所有重要数据到.mat文件
fprintf('正在保存数据到文件: %s\n', data_filename);
save(data_filename, ...
    'data', ...                    % 主要结果：多炮检波器数据
    'X', ...                      % 最后一炮的单炮数据
    'vp', 'vs', 'dens', ...       % 地层参数：速度和密度分布
    'lmd', 'miu', ...             % 弹性参数：Lamé常数和切变模量
    'p0', 'p1', 'p2', 'p3', ...   % FDTD系数矩阵
    'f', ...                      % Ricker子波震源函数
    'dx', 'dy', 'dz', 'dt', ...   % 网格参数：空间步长和时间步长
    'nx', 'ny', 'nz', 'maxt', ... % 网格尺寸和时间步数
    'pml', ...                    % PML边界厚度
    'num_s', 'N', ...             % 炮数和检波器数量
    'shot_start', 'shot_end', 'actual_num_shots', ... % 炮数范围控制参数
    'len_StoR', 'len_RtoR', ...   % 源距和检波器间距
    'med_x', 'med_y', ...         % 井孔中心坐标
    'f0', 'cal', ...              % 震源频率和井径
    'po1', 'vp1', 'vs1', ...      % 井孔介质参数
    'po2', 'vp2', 'vs2', ...      % 地层介质参数
    'execution_time', ...         % 程序执行时间
    'current_time');              % 数据生成时间

% 显示保存信息
fprintf('数据保存完成！\n');
fprintf('文件名: %s\n', data_filename);
fprintf('文件大小: %.2f MB\n', dir(data_filename).bytes/1024/1024);
fprintf('总执行时间: %.2f 秒\n', execution_time);
fprintf('总炮数: %d, 运行炮数: %d (第%d~%d炮), 检波器数: %d, 时间步数: %d\n', ...
        num_s, actual_num_shots, shot_start, shot_end, N, maxt);
fprintf('数据矩阵尺寸: %d × %d\n', size(data,1), size(data,2));

% 显示数据调用示例
fprintf('\n=== 三维数据调用示例 ===\n');
fprintf('load(''%s'');  %% 加载数据\n', data_filename);
fprintf('imagesc(data);  %% 显示所有运行的炮数据\n');
fprintf('plot(data(1,:));  %% 显示第1个运行炮的数据\n');
fprintf('注意：data矩阵第1行对应第%d炮，第%d行对应第%d炮\n', shot_start, actual_num_shots, shot_end);

fprintf('\n=== 三维程序运行完成 ===\n');
