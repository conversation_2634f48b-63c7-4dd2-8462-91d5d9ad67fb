% 三维声波测井正演结果显示程序
% 适配三维FDTD正演模拟结果的可视化显示

%% ==================== 用户配置区域 ====================
% 1. 数据文件选择
data_filename = 'acoustic_logging_3D_results.mat';

% 2. 显示模式选择
% 模式1：单检波器波形显示
% 模式2：三维波场切片显示
% 模式3：多位置检波器对比显示（如果有多个检波器数据）
display_mode = 1;

% 3. 波形显示配置
config.show_waveform = true;        % 是否显示时域波形
config.show_spectrum = true;        % 是否显示频谱分析
config.show_3d_snapshots = false;   % 是否显示三维波场快照（需要场数据）

% 4. 波形显示参数配置
config.amplitude_scale = 1.0;      % 振幅缩放因子
config.show_first_arrivals = true; % 是否标注首波到达
config.time_range = [];             % 显示时间范围[开始, 结束]秒，空值表示显示全部
config.fill_positive = true;       % 是否填充正振幅
config.fill_color = [0.8, 0.2, 0.2]; % 填充颜色 [R, G, B]

% 5. 频谱分析配置
config.freq_range = [0, 20000];     % 频率显示范围 [Hz]
config.window_type = 'hamming';     % 窗函数类型
config.nfft = 1024;                 % FFT点数

% 6. 图片质量配置
config.dpi = 300;                   % 图片分辨率 (300 DPI高清)
config.figure_size = [1200, 900];   % 图形窗口大小 [宽, 高]

% 7. 三维显示配置（如果有场数据）
config.slice_positions = [0.5, 0.5, 1.0]; % XYZ切片位置（归一化坐标）
config.colormap_name = 'seismic';   % 颜色映射
config.show_colorbar = true;        % 是否显示颜色条

%% ==================== 数据加载 ====================
% 加载三维正演结果数据文件
current_folder = pwd;
full_data_path = fullfile(current_folder, data_filename);

try
    loaded_data = load(full_data_path);
    fprintf('成功加载数据文件: %s\n', full_data_path);

    % 显示加载的变量
    fprintf('数据文件包含的变量:\n');
    field_names = fieldnames(loaded_data);
    for i = 1:length(field_names)
        var_name = field_names{i};
        var_size = size(loaded_data.(var_name));
        fprintf('  %s: %s\n', var_name, mat2str(var_size));
    end

catch ME
    error('数据加载失败: %s\n请确保文件 %s 存在', ME.message, data_filename);
end

% 提取三维正演结果的关键变量
if isfield(loaded_data, 'receiver_data')
    receiver_data = loaded_data.receiver_data;
    fprintf('\n检波器数据维度: %s\n', mat2str(size(receiver_data)));
else
    error('数据文件中未找到 receiver_data 变量');
end

if isfield(loaded_data, 'time_axis')
    time_axis = loaded_data.time_axis;
    dt = time_axis(2) - time_axis(1);
    nt = length(time_axis);
else
    error('数据文件中未找到 time_axis 变量');
end

% 提取几何和物理参数
receiver_x = loaded_data.receiver_x;
receiver_y = loaded_data.receiver_y;
receiver_z = loaded_data.receiver_z;
source_x = loaded_data.source_x;
source_y = loaded_data.source_y;
source_z = loaded_data.source_z;

% 提取网格参数
dx = loaded_data.dx;
dy = loaded_data.dy;
dz = loaded_data.dz;
nx = loaded_data.nx;
ny = loaded_data.ny;
nz = loaded_data.nz;

% 提取物理参数
f0 = loaded_data.f0;
vp_fluid = loaded_data.vp_fluid;
vp_rock = loaded_data.vp_rock;
well_radius = loaded_data.well_radius;

%% ==================== 主处理和显示 ====================
% 创建Picture文件夹
picture_dir = fullfile(pwd, 'Picture_3D_AcousticLogging');
if ~exist(picture_dir, 'dir')
    mkdir(picture_dir);
    fprintf('创建Picture文件夹: %s\n', picture_dir);
end

% 确定时间显示范围
if isempty(config.time_range)
    time_indices = 1:nt;
    display_time = time_axis;
else
    time_indices = find(time_axis >= config.time_range(1) & time_axis <= config.time_range(2));
    if isempty(time_indices)
        time_indices = 1:nt;
    end
    display_time = time_axis(time_indices);
end

% 提取显示用的波形数据
display_data = receiver_data(time_indices);

fprintf('\n============ 三维正演结果信息 ============\n');
fprintf('震源位置: (%.3f, %.3f, %.3f) m\n', source_x, source_y, source_z);
fprintf('检波器位置: (%.3f, %.3f, %.3f) m\n', receiver_x, receiver_y, receiver_z);
fprintf('源距: %.3f m\n', sqrt((receiver_x-source_x)^2 + (receiver_y-source_y)^2 + (receiver_z-source_z)^2));
fprintf('主频: %.0f Hz\n', f0);
fprintf('时间采样: %.2e s, 总时长: %.2f ms\n', dt, max(time_axis)*1000);
fprintf('网格尺寸: %d×%d×%d\n', nx, ny, nz);
fprintf('网格间距: dx=%.4f, dy=%.4f, dz=%.4f m\n', dx, dy, dz);

% 根据显示模式进行不同的可视化
switch display_mode
    case 1
        % 模式1：单检波器波形显示
        create_waveform_display(display_data, display_time, config, ...
                               receiver_x, receiver_y, receiver_z, ...
                               source_x, source_y, source_z, f0);

        % 保存波形图
        fig_filename = fullfile(picture_dir, '三维正演_波形显示.png');
        save_high_quality_figure(fig_filename, config.dpi);

        if config.show_spectrum
            % 频谱分析显示
            create_spectrum_display(display_data, dt, config, f0);

            % 保存频谱图
            fig_filename = fullfile(picture_dir, '三维正演_频谱分析.png');
            save_high_quality_figure(fig_filename, config.dpi);
        end

    case 2
        % 模式2：三维波场切片显示（需要场数据）
        fprintf('模式2：三维波场切片显示（需要完整场数据）\n');
        fprintf('当前数据只包含检波器记录，无法显示三维波场\n');

    case 3
        % 模式3：多位置检波器对比显示
        fprintf('模式3：多位置检波器对比显示（需要多个检波器数据）\n');
        fprintf('当前数据只包含单个检波器记录\n');

    otherwise
        error('无效的显示模式: %d', display_mode);
end

fprintf('\n所有图片已保存到: %s\n', picture_dir);
fprintf('程序执行完成！\n');

%% ==================== 辅助函数 ====================
function create_waveform_display(waveform_data, time_vec, config, ...
                                 rx, ry, rz, sx, sy, sz, f0)
    % 创建三维正演结果的波形显示

    % 创建高质量图形窗口
    figure('Position', [100, 100, config.figure_size], 'Color', 'w', ...
           'Name', '三维声波测井正演结果', 'NumberTitle', 'off');

    % 子图1：时域波形
    subplot(2,1,1);
    hold on;

    % 归一化波形数据
    max_amplitude = max(abs(waveform_data));
    if max_amplitude < 1e-10
        max_amplitude = 1;
    end
    norm_data = waveform_data / max_amplitude * config.amplitude_scale;

    % 绘制基线
    plot([time_vec(1), time_vec(end)], [0, 0], 'k--', 'LineWidth', 0.8, 'Color', [0.5, 0.5, 0.5]);

    % 绘制波形
    plot(time_vec*1e6, norm_data, 'b-', 'LineWidth', 1.5);

    % 填充正振幅（如果启用）
    if config.fill_positive
        positive_idx = norm_data > 0;
        if any(positive_idx)
            % 创建填充区域
            fill_x = [time_vec(positive_idx)*1e6; flipud(time_vec(positive_idx)*1e6)];
            fill_y = [norm_data(positive_idx); zeros(sum(positive_idx), 1)];
            fill(fill_x, fill_y, config.fill_color, 'EdgeColor', 'none', 'FaceAlpha', 0.6);
        end
    end

    % 检测并标注首波到达
    if config.show_first_arrivals
        first_arrival_time = detect_first_arrival_simple(waveform_data, time_vec);
        if first_arrival_time > 0
            first_arrival_idx = find(time_vec >= first_arrival_time, 1, 'first');
            plot(first_arrival_time*1e6, norm_data(first_arrival_idx), 'ro', ...
                 'MarkerSize', 8, 'MarkerFaceColor', 'r', 'MarkerEdgeColor', 'k', 'LineWidth', 2);
            text(first_arrival_time*1e6, norm_data(first_arrival_idx) + 0.1, ...
                 sprintf('首至: %.1f μs', first_arrival_time*1e6), ...
                 'FontSize', 10, 'Color', 'r', 'FontWeight', 'bold');
        end
    end

    % 设置图形属性
    xlabel('时间 (μs)', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('归一化振幅', 'FontSize', 12, 'FontWeight', 'bold');
    title('三维FDTD正演波形记录', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    set(gca, 'GridLineStyle', ':', 'GridAlpha', 0.3);

    % 子图2：几何信息显示
    subplot(2,1,2);
    hold on;

    % 绘制井筒示意图
    well_depth = max(sz, rz) + 0.5;
    plot([0, 0], [0, well_depth], 'k-', 'LineWidth', 3); % 井筒

    % 标注震源和检波器位置
    plot(0, sz, 'r*', 'MarkerSize', 12, 'LineWidth', 2); % 震源
    plot(0, rz, 'bo', 'MarkerSize', 8, 'LineWidth', 2); % 检波器

    % 添加标注
    text(0.05, sz, sprintf('震源 (%.2f m)', sz), 'FontSize', 10, 'Color', 'r');
    text(0.05, rz, sprintf('检波器 (%.2f m)', rz), 'FontSize', 10, 'Color', 'b');

    % 设置图形属性
    xlabel('径向距离 (m)', 'FontSize', 12);
    ylabel('深度 (m)', 'FontSize', 12);
    title(sprintf('测井几何配置 (源距: %.2f m, 主频: %.0f Hz)', abs(rz-sz), f0), ...
          'FontSize', 12, 'FontWeight', 'bold');
    grid on;
    set(gca, 'YDir', 'reverse'); % 深度向下
    axis equal;
    xlim([-0.2, 0.3]);
    ylim([0, well_depth]);

    % 调整子图间距
    set(gcf, 'PaperPositionMode', 'auto');
end

function create_spectrum_display(waveform_data, dt, config, f0)
    % 创建频谱分析显示

    % 创建新的图形窗口
    figure('Position', [200, 200, config.figure_size], 'Color', 'w', ...
           'Name', '频谱分析', 'NumberTitle', 'off');

    % 计算频谱
    N = length(waveform_data);
    if N < config.nfft
        % 零填充
        padded_data = [waveform_data; zeros(config.nfft - N, 1)];
    else
        padded_data = waveform_data(1:config.nfft);
    end

    % 应用窗函数
    switch lower(config.window_type)
        case 'hamming'
            window = hamming(length(padded_data));
        case 'hanning'
            window = hanning(length(padded_data));
        case 'blackman'
            window = blackman(length(padded_data));
        otherwise
            window = ones(length(padded_data), 1);
    end

    windowed_data = padded_data .* window;

    % FFT计算
    Y = fft(windowed_data);
    f = (0:config.nfft-1) * (1/dt) / config.nfft;

    % 只取正频率部分
    pos_freq_idx = 1:floor(config.nfft/2);
    f_pos = f(pos_freq_idx);
    Y_pos = Y(pos_freq_idx);

    % 计算幅度谱和相位谱
    amplitude_spectrum = abs(Y_pos);
    phase_spectrum = angle(Y_pos);

    % 子图1：幅度谱
    subplot(2,1,1);
    plot(f_pos, 20*log10(amplitude_spectrum + eps), 'b-', 'LineWidth', 1.5);
    hold on;

    % 标注主频
    [~, peak_idx] = max(amplitude_spectrum);
    peak_freq = f_pos(peak_idx);
    plot(peak_freq, 20*log10(amplitude_spectrum(peak_idx)), 'ro', ...
         'MarkerSize', 8, 'MarkerFaceColor', 'r');
    text(peak_freq, 20*log10(amplitude_spectrum(peak_idx)) + 5, ...
         sprintf('峰值: %.0f Hz', peak_freq), 'FontSize', 10, 'Color', 'r');

    % 标注理论主频
    if f0 <= max(f_pos)
        plot([f0, f0], ylim, 'r--', 'LineWidth', 1);
        text(f0, max(ylim) - 10, sprintf('理论主频: %.0f Hz', f0), ...
             'FontSize', 10, 'Color', 'r', 'Rotation', 90);
    end

    xlabel('频率 (Hz)', 'FontSize', 12);
    ylabel('幅度 (dB)', 'FontSize', 12);
    title('幅度谱', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    xlim(config.freq_range);

    % 子图2：相位谱
    subplot(2,1,2);
    plot(f_pos, phase_spectrum*180/pi, 'g-', 'LineWidth', 1.5);
    xlabel('频率 (Hz)', 'FontSize', 12);
    ylabel('相位 (度)', 'FontSize', 12);
    title('相位谱', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    xlim(config.freq_range);

    set(gcf, 'PaperPositionMode', 'auto');
end

function save_high_quality_figure(filename, dpi)
    % 保存高质量图形
    set(gcf, 'PaperPositionMode', 'auto');
    set(gcf, 'PaperUnits', 'inches');
    set(gcf, 'PaperSize', [12, 9]);

    print(gcf, filename, '-dpng', sprintf('-r%d', dpi));
    fprintf('保存高清图片 (%d DPI): %s\n', dpi, filename);

    close(gcf);
end

function first_arrival_time = detect_first_arrival_simple(data, time_vec)
    % 简单的首波到达检测
    abs_data = abs(data);
    threshold = 0.05 * max(abs_data);
    first_idx = find(abs_data > threshold, 1, 'first');

    if ~isempty(first_idx) && first_idx > 1
        first_arrival_time = time_vec(first_idx);
    else
        first_arrival_time = -1;
    end
end
