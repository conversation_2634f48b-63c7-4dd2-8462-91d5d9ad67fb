% 三维声波测井正演结果显示程序
% 适配三维FDTD正演模拟结果的可视化显示

%% ==================== 用户配置区域 ====================
% 1. 数据文件选择
data_filename = 'acoustic_logging_13receiver_results.mat';

% 2. 显示模式选择
% 模式1：多检波器声波测井标准显示（类似原始程序）
% 模式2：单检波器波形和频谱分析
% 模式3：多检波器对比显示
display_mode = 3;

% 3. 检波器显示配置（模式1使用）
config.num_receivers_to_show = 8;   % 每次显示的检波器数量
config.receiver_start = 1;          % 起始检波器编号
config.trace_spacing = 1.5;         % 道间距（垂直偏移）

% 4. 波形显示配置
config.show_waveform = true;        % 是否显示时域波形
config.show_spectrum = true;        % 是否显示频谱分析
config.show_first_arrivals = true;  % 是否标注首波到达

% 4. 波形显示参数配置
config.amplitude_scale = 1.0;      % 振幅缩放因子
config.show_first_arrivals = true; % 是否标注首波到达
config.time_range = [];             % 显示时间范围[开始, 结束]秒，空值表示显示全部
config.fill_positive = true;       % 是否填充正振幅
config.fill_color = [0.8, 0.2, 0.2]; % 填充颜色 [R, G, B]

% 5. 频谱分析配置
config.freq_range = [0, 20000];     % 频率显示范围 [Hz]
config.window_type = 'hamming';     % 窗函数类型
config.nfft = 1024;                 % FFT点数

% 6. 图片质量配置
config.dpi = 300;                   % 图片分辨率 (300 DPI高清)
config.figure_size = [1200, 900];   % 图形窗口大小 [宽, 高]

% 7. 三维显示配置（如果有场数据）
config.slice_positions = [0.5, 0.5, 1.0]; % XYZ切片位置（归一化坐标）
config.colormap_name = 'seismic';   % 颜色映射
config.show_colorbar = true;        % 是否显示颜色条

%% ==================== 数据加载 ====================
% 加载多检波器声波测井数据文件
current_folder = pwd;
full_data_path = fullfile(current_folder, data_filename);

try
    loaded_data = load(full_data_path);
    fprintf('成功加载数据文件: %s\n', full_data_path);

    % 显示加载的变量
    fprintf('数据文件包含的变量:\n');
    field_names = fieldnames(loaded_data);
    for i = 1:length(field_names)
        var_name = field_names{i};
        var_size = size(loaded_data.(var_name));
        fprintf('  %s: %s\n', var_name, mat2str(var_size));
    end

catch ME
    error('数据加载失败: %s\n请确保文件 %s 存在', ME.message, data_filename);
end

% 提取多检波器数据的关键变量
if isfield(loaded_data, 'receiver_data')
    receiver_data = loaded_data.receiver_data;  % [nt × num_receivers]
    [nt, num_receivers] = size(receiver_data);
    fprintf('\n检波器数据维度: %s (时间×检波器)\n', mat2str(size(receiver_data)));
else
    error('数据文件中未找到 receiver_data 变量');
end

if isfield(loaded_data, 'time_axis')
    time_axis = loaded_data.time_axis;
    dt = loaded_data.dt;
else
    error('数据文件中未找到 time_axis 变量');
end

% 提取几何和物理参数
receiver_x = loaded_data.receiver_x;  % [num_receivers × 1]
receiver_y = loaded_data.receiver_y;  % [num_receivers × 1]
receiver_z = loaded_data.receiver_z;  % [num_receivers × 1]
source_x = loaded_data.source_x;      % 标量
source_y = loaded_data.source_y;      % 标量
source_z = loaded_data.source_z;      % 标量

% 提取网格参数
dx = loaded_data.dx;
dy = loaded_data.dy;
dz = loaded_data.dz;
nx = loaded_data.nx;
ny = loaded_data.ny;
nz = loaded_data.nz;

% 提取物理参数
f0 = loaded_data.f0;
vp_fluid = loaded_data.vp_fluid;
vp_rock = loaded_data.vp_rock;
well_radius = loaded_data.well_radius;

% 计算源距
source_distances = sqrt((receiver_x - source_x).^2 + ...
                       (receiver_y - source_y).^2 + ...
                       (receiver_z - source_z).^2);

%% ==================== 主处理和显示 ====================
% 创建Picture文件夹
picture_dir = fullfile(pwd, 'Picture_MultiReceiver_AcousticLogging');
if ~exist(picture_dir, 'dir')
    mkdir(picture_dir);
    fprintf('创建Picture文件夹: %s\n', picture_dir);
end

% 确定时间显示范围
if isempty(config.time_range)
    time_indices = 1:nt;
    display_time = time_axis;
else
    time_indices = find(time_axis >= config.time_range(1) & time_axis <= config.time_range(2));
    if isempty(time_indices)
        time_indices = 1:nt;
    end
    display_time = time_axis(time_indices);
end

fprintf('\n============ 多检波器声波测井结果信息 ============\n');
fprintf('震源位置: (%.3f, %.3f, %.3f) m\n', source_x, source_y, source_z);
fprintf('检波器数量: %d\n', num_receivers);
fprintf('检波器深度范围: %.3f - %.3f m\n', min(receiver_z), max(receiver_z));
fprintf('源距范围: %.3f - %.3f m\n', min(source_distances), max(source_distances));
fprintf('主频: %.0f Hz\n', f0);
fprintf('时间采样: %.2e s, 总时长: %.2f ms\n', dt, max(time_axis)*1000);
fprintf('网格尺寸: %d×%d×%d\n', nx, ny, nz);
fprintf('网格间距: dx=%.4f, dy=%.4f, dz=%.4f m\n', dx, dy, dz);

% 根据显示模式进行不同的可视化
switch display_mode
    case 1
        % 模式1：多检波器声波测井标准显示
        create_multi_receiver_display(receiver_data, display_time, time_indices, config, ...
                                     receiver_z, source_z, source_distances, f0, num_receivers);

        % 保存多检波器显示图
        fig_filename = fullfile(picture_dir, '多检波器声波测井显示.png');
        save_high_quality_figure(fig_filename, config.dpi);

    case 2
        % 模式2：单检波器波形和频谱分析
        selected_receiver = 1; % 选择第一个检波器
        display_data = receiver_data(time_indices, selected_receiver);

        create_single_receiver_analysis(display_data, display_time, config, ...
                                       receiver_z(selected_receiver), source_z, ...
                                       source_distances(selected_receiver), f0, selected_receiver);

        % 保存单检波器分析图
        fig_filename = fullfile(picture_dir, sprintf('检波器%d_波形频谱分析.png', selected_receiver));
        save_high_quality_figure(fig_filename, config.dpi);

        if config.show_spectrum
            % 频谱分析显示
            create_spectrum_display(display_data, dt, config, f0);

            % 保存频谱图
            fig_filename = fullfile(picture_dir, sprintf('检波器%d_频谱分析.png', selected_receiver));
            save_high_quality_figure(fig_filename, config.dpi);
        end

    case 3
        % 模式3：多检波器对比显示
        create_receiver_comparison_display(receiver_data, display_time, time_indices, config, ...
                                          receiver_z, source_z, source_distances, f0, num_receivers);

        % 保存对比显示图
        fig_filename = fullfile(picture_dir, '多检波器对比显示.png');
        save_high_quality_figure(fig_filename, config.dpi);

    otherwise
        error('无效的显示模式: %d', display_mode);
end

fprintf('\n所有图片已保存到: %s\n', picture_dir);
fprintf('程序执行完成！\n');

%% ==================== 辅助函数 ====================
function create_multi_receiver_display(receiver_data, display_time, time_indices, config, ...
                                      receiver_z, source_z, source_distances, f0, num_receivers)
    % 创建多检波器声波测井标准显示（类似原始程序风格）

    % 创建高质量图形窗口
    figure('Position', [100, 100, config.figure_size], 'Color', 'w', ...
           'Name', '多检波器声波测井显示', 'NumberTitle', 'off');
    hold on;

    % 计算实际显示的检波器范围
    receiver_start = config.receiver_start;
    receiver_end = min(receiver_start + config.num_receivers_to_show - 1, num_receivers);
    actual_receivers = receiver_end - receiver_start + 1;

    % 计算全局归一化因子
    display_data = receiver_data(time_indices, receiver_start:receiver_end);
    max_amplitude = max(abs(display_data(:)));
    if max_amplitude < 1e-10
        max_amplitude = 1;
    end

    % 绘制每道数据
    first_arrivals = [];
    for i = 1:actual_receivers
        receiver_idx = receiver_start + i - 1;
        trace_offset = i * config.trace_spacing;

        % 提取当前检波器的数据
        trace_data = receiver_data(time_indices, receiver_idx);

        % 归一化处理
        norm_data = trace_data / max_amplitude * config.amplitude_scale;

        % 绘制基线
        plot([display_time(1), display_time(end)]*1e6, [trace_offset, trace_offset], ...
             'k-', 'LineWidth', 0.5, 'Color', [0.7, 0.7, 0.7]);

        % 绘制波形
        wave_y = norm_data + trace_offset;
        plot(display_time*1e6, wave_y, 'k-', 'LineWidth', 1.2);

        % 填充正振幅（如果启用）
        if config.fill_positive
            positive_idx = norm_data > 0;
            if any(positive_idx)
                % 找到连续的正振幅区间
                positive_idx = positive_idx(:);
                diff_idx = diff([false; positive_idx; false]);
                start_idx = find(diff_idx == 1);
                end_idx = find(diff_idx == -1) - 1;

                % 对每个连续区间分别填充
                for seg = 1:length(start_idx)
                    seg_start = start_idx(seg);
                    seg_end = end_idx(seg);

                    seg_time = display_time(seg_start:seg_end)*1e6;
                    seg_wave = wave_y(seg_start:seg_end);
                    seg_baseline = trace_offset * ones(size(seg_time));

                    % 确保所有数组都是行向量
                    seg_time = seg_time(:)';
                    seg_wave = seg_wave(:)';
                    seg_baseline = seg_baseline(:)';

                    fill_x = [seg_time, fliplr(seg_time)];
                    fill_y = [seg_wave, fliplr(seg_baseline)];
                    fill(fill_x, fill_y, config.fill_color, 'EdgeColor', 'none', 'FaceAlpha', 0.6);
                end
            end
        end

        % 检测首波到达（如果启用）
        if config.show_first_arrivals
            first_arrival_time = detect_first_arrival_simple(trace_data, display_time);
            if first_arrival_time > 0
                plot(first_arrival_time*1e6, trace_offset, 'ro', 'MarkerSize', 6, ...
                     'MarkerFaceColor', 'r', 'MarkerEdgeColor', 'k', 'LineWidth', 1);
                first_arrivals = [first_arrivals; first_arrival_time];
            end
        end
    end

    % 设置图形属性
    xlabel('时间 (μs)', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('检波器道号', 'FontSize', 14, 'FontWeight', 'bold');

    title_str = sprintf('多检波器声波测井数据 (主频: %.0f Hz)', f0);
    title(title_str, 'FontSize', 16, 'FontWeight', 'bold');

    % 网格和轴设置
    grid on;
    set(gca, 'LineWidth', 1.2, 'GridLineStyle', ':', 'GridAlpha', 0.3);
    set(gca, 'FontSize', 11);

    % Y轴设置 - 显示检波器编号和深度
    yticks(config.trace_spacing:config.trace_spacing:actual_receivers*config.trace_spacing);
    y_labels = cell(1, actual_receivers);
    for i = 1:actual_receivers
        receiver_idx = receiver_start + i - 1;
        y_labels{i} = sprintf('R%d (%.2fm)', receiver_idx, receiver_z(receiver_idx));
    end
    yticklabels(y_labels);

    % 范围设置
    xlim([display_time(1), display_time(end)]*1e6);
    ylim([0.5*config.trace_spacing, (actual_receivers+0.5)*config.trace_spacing]);

    set(gcf, 'PaperPositionMode', 'auto');
end

function create_single_receiver_analysis(waveform_data, time_vec, config, ...
                                        receiver_depth, source_depth, source_distance, f0, receiver_num)
    % 创建单检波器波形和几何分析显示

    % 创建高质量图形窗口
    figure('Position', [200, 200, config.figure_size], 'Color', 'w', ...
           'Name', sprintf('检波器%d分析', receiver_num), 'NumberTitle', 'off');

    % 子图1：时域波形
    subplot(2,1,1);
    hold on;

    % 归一化波形数据
    max_amplitude = max(abs(waveform_data));
    if max_amplitude < 1e-10
        max_amplitude = 1;
    end
    norm_data = waveform_data / max_amplitude * config.amplitude_scale;

    % 绘制基线
    plot([time_vec(1), time_vec(end)]*1e6, [0, 0], 'k--', 'LineWidth', 0.8, 'Color', [0.5, 0.5, 0.5]);

    % 绘制波形
    plot(time_vec*1e6, norm_data, 'b-', 'LineWidth', 1.5);

    % 填充正振幅（如果启用）
    if config.fill_positive
        positive_idx = norm_data > 0;
        if any(positive_idx)
            % 确保数组维度一致
            pos_time = time_vec(positive_idx)*1e6;
            pos_data = norm_data(positive_idx);

            % 转换为列向量
            pos_time = pos_time(:);
            pos_data = pos_data(:);

            fill_x = [pos_time; flipud(pos_time)];
            fill_y = [pos_data; zeros(length(pos_data), 1)];
            fill(fill_x, fill_y, config.fill_color, 'EdgeColor', 'none', 'FaceAlpha', 0.6);
        end
    end

    % 检测并标注首波到达
    if config.show_first_arrivals
        first_arrival_time = detect_first_arrival_simple(waveform_data, time_vec);
        if first_arrival_time > 0
            first_arrival_idx = find(time_vec >= first_arrival_time, 1, 'first');
            plot(first_arrival_time*1e6, norm_data(first_arrival_idx), 'ro', ...
                 'MarkerSize', 8, 'MarkerFaceColor', 'r', 'MarkerEdgeColor', 'k', 'LineWidth', 2);
            text(first_arrival_time*1e6, norm_data(first_arrival_idx) + 0.1, ...
                 sprintf('首至: %.1f μs', first_arrival_time*1e6), ...
                 'FontSize', 10, 'Color', 'r', 'FontWeight', 'bold');
        end
    end

    % 设置图形属性
    xlabel('时间 (μs)', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('归一化振幅', 'FontSize', 12, 'FontWeight', 'bold');
    title(sprintf('检波器%d波形记录 (深度: %.2fm)', receiver_num, receiver_depth), ...
          'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    set(gca, 'GridLineStyle', ':', 'GridAlpha', 0.3);

    % 子图2：几何信息显示
    subplot(2,1,2);
    hold on;

    % 绘制井筒示意图
    well_depth = max(source_depth, receiver_depth) + 0.5;
    plot([0, 0], [0, well_depth], 'k-', 'LineWidth', 3); % 井筒

    % 标注震源和检波器位置
    plot(0, source_depth, 'r*', 'MarkerSize', 12, 'LineWidth', 2); % 震源
    plot(0, receiver_depth, 'bo', 'MarkerSize', 8, 'LineWidth', 2); % 检波器

    % 添加标注
    text(0.05, source_depth, sprintf('震源 (%.2f m)', source_depth), 'FontSize', 10, 'Color', 'r');
    text(0.05, receiver_depth, sprintf('检波器%d (%.2f m)', receiver_num, receiver_depth), ...
         'FontSize', 10, 'Color', 'b');

    % 设置图形属性
    xlabel('径向距离 (m)', 'FontSize', 12);
    ylabel('深度 (m)', 'FontSize', 12);
    title(sprintf('测井几何配置 (源距: %.2f m, 主频: %.0f Hz)', source_distance, f0), ...
          'FontSize', 12, 'FontWeight', 'bold');
    grid on;
    set(gca, 'YDir', 'reverse'); % 深度向下
    axis equal;
    xlim([-0.2, 0.3]);
    ylim([0, well_depth]);

    set(gcf, 'PaperPositionMode', 'auto');
end

function create_receiver_comparison_display(receiver_data, display_time, time_indices, config, ...
                                           receiver_z, source_z, source_distances, f0, num_receivers)
    % 创建多检波器对比显示

    % 创建高质量图形窗口
    figure('Position', [300, 300, config.figure_size], 'Color', 'w', ...
           'Name', '多检波器对比显示', 'NumberTitle', 'off');

    % 子图1：归一化波形叠加显示
    subplot(2,1,1);
    hold on;

    colors = lines(num_receivers); % 生成不同颜色

    for i = 1:num_receivers
        trace_data = receiver_data(time_indices, i);
        max_amp = max(abs(trace_data));
        if max_amp > 1e-10
            norm_data = trace_data / max_amp;
        else
            norm_data = trace_data;
        end

        plot(display_time*1e6, norm_data, 'Color', colors(i,:), 'LineWidth', 1.2, ...
             'DisplayName', sprintf('R%d (%.2fm)', i, receiver_z(i)));
    end

    xlabel('时间 (μs)', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('归一化振幅', 'FontSize', 12, 'FontWeight', 'bold');
    title('多检波器波形对比 (各道独立归一化)', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    legend('Location', 'best', 'FontSize', 8);

    % 子图2：源距-时间关系
    subplot(2,1,2);
    hold on;

    % 检测各检波器的首波到达时间
    first_arrivals = zeros(num_receivers, 1);
    for i = 1:num_receivers
        trace_data = receiver_data(time_indices, i);
        first_arrival_time = detect_first_arrival_simple(trace_data, display_time);
        if first_arrival_time > 0
            first_arrivals(i) = first_arrival_time;
        else
            first_arrivals(i) = NaN;
        end
    end

    % 绘制源距-首波到达时间关系
    valid_idx = ~isnan(first_arrivals);
    if sum(valid_idx) > 1
        plot(source_distances(valid_idx), first_arrivals(valid_idx)*1e6, 'bo-', ...
             'MarkerSize', 6, 'LineWidth', 1.5, 'MarkerFaceColor', 'b');

        % 拟合直线（估算波速）
        p = polyfit(source_distances(valid_idx), first_arrivals(valid_idx)*1e6, 1);
        fit_line = polyval(p, source_distances(valid_idx));
        plot(source_distances(valid_idx), fit_line, 'r--', 'LineWidth', 1.5);

        % 计算波速
        velocity = 1e6 / p(1); % m/s
        text(0.7*max(source_distances), 0.8*max(first_arrivals(valid_idx)*1e6), ...
             sprintf('估算波速: %.0f m/s', velocity), ...
             'FontSize', 12, 'Color', 'r', 'FontWeight', 'bold', ...
             'BackgroundColor', 'w', 'EdgeColor', 'k');
    end

    xlabel('源距 (m)', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('首波到达时间 (μs)', 'FontSize', 12, 'FontWeight', 'bold');
    title(sprintf('源距-首波到达时间关系 (主频: %.0f Hz)', f0), ...
          'FontSize', 14, 'FontWeight', 'bold');
    grid on;

    set(gcf, 'PaperPositionMode', 'auto');
end

function create_spectrum_display(waveform_data, dt, config, f0)
    % 创建频谱分析显示

    % 创建新的图形窗口
    figure('Position', [400, 400, config.figure_size], 'Color', 'w', ...
           'Name', '频谱分析', 'NumberTitle', 'off');

    % 计算频谱
    N = length(waveform_data);
    if N < config.nfft
        % 零填充
        padded_data = [waveform_data; zeros(config.nfft - N, 1)];
    else
        padded_data = waveform_data(1:config.nfft);
    end

    % 应用窗函数
    switch lower(config.window_type)
        case 'hamming'
            window = hamming(length(padded_data));
        case 'hanning'
            window = hanning(length(padded_data));
        case 'blackman'
            window = blackman(length(padded_data));
        otherwise
            window = ones(length(padded_data), 1);
    end

    windowed_data = padded_data .* window;

    % FFT计算
    Y = fft(windowed_data);
    f = (0:config.nfft-1) * (1/dt) / config.nfft;

    % 只取正频率部分
    pos_freq_idx = 1:floor(config.nfft/2);
    f_pos = f(pos_freq_idx);
    Y_pos = Y(pos_freq_idx);

    % 计算幅度谱和相位谱
    amplitude_spectrum = abs(Y_pos);
    phase_spectrum = angle(Y_pos);

    % 子图1：幅度谱
    subplot(2,1,1);
    plot(f_pos, 20*log10(amplitude_spectrum + eps), 'b-', 'LineWidth', 1.5);
    hold on;

    % 标注主频
    [~, peak_idx] = max(amplitude_spectrum);
    peak_freq = f_pos(peak_idx);
    plot(peak_freq, 20*log10(amplitude_spectrum(peak_idx)), 'ro', ...
         'MarkerSize', 8, 'MarkerFaceColor', 'r');
    text(peak_freq, 20*log10(amplitude_spectrum(peak_idx)) + 5, ...
         sprintf('峰值: %.0f Hz', peak_freq), 'FontSize', 10, 'Color', 'r');

    % 标注理论主频
    if f0 <= max(f_pos)
        plot([f0, f0], ylim, 'r--', 'LineWidth', 1);
        text(f0, max(ylim) - 10, sprintf('理论主频: %.0f Hz', f0), ...
             'FontSize', 10, 'Color', 'r', 'Rotation', 90);
    end

    xlabel('频率 (Hz)', 'FontSize', 12);
    ylabel('幅度 (dB)', 'FontSize', 12);
    title('幅度谱', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    xlim(config.freq_range);

    % 子图2：相位谱
    subplot(2,1,2);
    plot(f_pos, phase_spectrum*180/pi, 'g-', 'LineWidth', 1.5);
    xlabel('频率 (Hz)', 'FontSize', 12);
    ylabel('相位 (度)', 'FontSize', 12);
    title('相位谱', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    xlim(config.freq_range);

    set(gcf, 'PaperPositionMode', 'auto');
end

function save_high_quality_figure(filename, dpi)
    % 保存高质量图形
    set(gcf, 'PaperPositionMode', 'auto');
    set(gcf, 'PaperUnits', 'inches');
    set(gcf, 'PaperSize', [12, 9]);

    print(gcf, filename, '-dpng', sprintf('-r%d', dpi));
    fprintf('保存高清图片 (%d DPI): %s\n', dpi, filename);

    close(gcf);
end

function first_arrival_time = detect_first_arrival_simple(data, time_vec)
    % 简单的首波到达检测
    abs_data = abs(data);
    threshold = 0.05 * max(abs_data);
    first_idx = find(abs_data > threshold, 1, 'first');

    if ~isempty(first_idx) && first_idx > 1
        first_arrival_time = time_vec(first_idx);
    else
        first_arrival_time = -1;
    end
end
