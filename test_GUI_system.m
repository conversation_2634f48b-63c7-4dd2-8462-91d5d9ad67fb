function test_GUI_system()
%% GUI系统测试脚本
% 功能：全面测试二维FDTD声波测井仿真系统GUI的各项功能
% 用于验证系统稳定性和功能完整性

    clc;
    fprintf('=== GUI系统功能测试 ===\n\n');
    
    % 测试结果记录
    test_results = struct();
    test_count = 0;
    pass_count = 0;
    
    try
        % 1. 测试GUI启动
        [result, msg] = testGUIStartup();
        test_count = test_count + 1;
        if result
            pass_count = pass_count + 1;
            fprintf('✅ GUI启动测试: 通过\n');
        else
            fprintf('❌ GUI启动测试: 失败 - %s\n', msg);
        end
        test_results.gui_startup = struct('result', result, 'message', msg);
        
        % 2. 测试参数控件
        [result, msg] = testParameterControls();
        test_count = test_count + 1;
        if result
            pass_count = pass_count + 1;
            fprintf('✅ 参数控件测试: 通过\n');
        else
            fprintf('❌ 参数控件测试: 失败 - %s\n', msg);
        end
        test_results.parameter_controls = struct('result', result, 'message', msg);
        
        % 3. 测试参数验证
        [result, msg] = testParameterValidation();
        test_count = test_count + 1;
        if result
            pass_count = pass_count + 1;
            fprintf('✅ 参数验证测试: 通过\n');
        else
            fprintf('❌ 参数验证测试: 失败 - %s\n', msg);
        end
        test_results.parameter_validation = struct('result', result, 'message', msg);
        
        % 4. 测试文件操作
        [result, msg] = testFileOperations();
        test_count = test_count + 1;
        if result
            pass_count = pass_count + 1;
            fprintf('✅ 文件操作测试: 通过\n');
        else
            fprintf('❌ 文件操作测试: 失败 - %s\n', msg);
        end
        test_results.file_operations = struct('result', result, 'message', msg);
        
        % 5. 测试结果显示
        [result, msg] = testResultDisplay();
        test_count = test_count + 1;
        if result
            pass_count = pass_count + 1;
            fprintf('✅ 结果显示测试: 通过\n');
        else
            fprintf('❌ 结果显示测试: 失败 - %s\n', msg);
        end
        test_results.result_display = struct('result', result, 'message', msg);
        
        % 6. 测试界面响应
        [result, msg] = testInterfaceResponse();
        test_count = test_count + 1;
        if result
            pass_count = pass_count + 1;
            fprintf('✅ 界面响应测试: 通过\n');
        else
            fprintf('❌ 界面响应测试: 失败 - %s\n', msg);
        end
        test_results.interface_response = struct('result', result, 'message', msg);
        
    catch ME
        fprintf('❌ 测试过程中发生严重错误: %s\n', ME.message);
        test_results.critical_error = ME.message;
    end
    
    % 输出测试总结
    fprintf('\n=== 测试总结 ===\n');
    fprintf('总测试项: %d\n', test_count);
    fprintf('通过项数: %d\n', pass_count);
    fprintf('失败项数: %d\n', test_count - pass_count);
    fprintf('通过率: %.1f%%\n', (pass_count/test_count)*100);
    
    if pass_count == test_count
        fprintf('🎉 所有测试通过！系统可以用于专利申请演示。\n');
    else
        fprintf('⚠️  部分测试失败，建议检查相关功能。\n');
    end
    
    % 保存测试结果
    save('GUI_test_results.mat', 'test_results');
    fprintf('\n测试结果已保存到: GUI_test_results.mat\n');
    
    fprintf('================\n\n');
end

function [result, msg] = testGUIStartup()
    %% 测试GUI启动功能
    
    try
        % 检查是否已有GUI窗口
        existing_fig = findobj('Name', '二维FDTD声波测井正演模拟系统');
        
        if isempty(existing_fig)
            % 启动GUI
            FDTD_GUI_Main();
            pause(1); % 等待界面加载
        end
        
        % 验证GUI是否成功创建
        gui_fig = findobj('Name', '二维FDTD声波测井正演模拟系统');
        
        if isempty(gui_fig)
            result = false;
            msg = 'GUI窗口未成功创建';
        else
            % 检查主要组件是否存在
            main_components = {'parameter_panel', 'result_panel'};
            missing_components = {};
            
            for i = 1:length(main_components)
                if isempty(findobj('Tag', main_components{i}))
                    missing_components{end+1} = main_components{i};
                end
            end
            
            if isempty(missing_components)
                result = true;
                msg = 'GUI启动成功，所有主要组件正常';
            else
                result = false;
                msg = sprintf('缺少组件: %s', strjoin(missing_components, ', '));
            end
        end
        
    catch ME
        result = false;
        msg = ME.message;
    end
end

function [result, msg] = testParameterControls()
    %% 测试参数控件功能
    
    try
        % 测试参数列表
        test_params = {'po1', 'vp1', 'f0', 'cal', 'num_s'};
        missing_controls = {};
        
        for i = 1:length(test_params)
            param_name = test_params{i};
            
            % 检查编辑框
            edit_handle = findobj('Tag', [param_name '_edit']);
            if isempty(edit_handle)
                missing_controls{end+1} = [param_name '_edit'];
            end
            
            % 检查滑块
            slider_handle = findobj('Tag', [param_name '_slider']);
            if isempty(slider_handle)
                missing_controls{end+1} = [param_name '_slider'];
            end
        end
        
        if isempty(missing_controls)
            result = true;
            msg = '所有参数控件正常';
        else
            result = false;
            msg = sprintf('缺少控件: %s', strjoin(missing_controls, ', '));
        end
        
    catch ME
        result = false;
        msg = ME.message;
    end
end

function [result, msg] = testParameterValidation()
    %% 测试参数验证功能
    
    try
        % 测试参数范围验证
        test_cases = {
            'f0', 15000, true;   % 正常值
            'f0', -1000, false;  % 异常值
            'cal', 0.1, true;    % 正常值
            'cal', -0.5, false;  % 异常值
        };
        
        validation_errors = {};
        
        for i = 1:size(test_cases, 1)
            param_name = test_cases{i, 1};
            test_value = test_cases{i, 2};
            should_pass = test_cases{i, 3};
            
            % 尝试设置参数值
            edit_handle = findobj('Tag', [param_name '_edit']);
            if ~isempty(edit_handle)
                old_value = get(edit_handle, 'String');
                set(edit_handle, 'String', num2str(test_value));
                
                % 触发验证
                callback = get(edit_handle, 'Callback');
                if ~isempty(callback)
                    try
                        feval(callback, edit_handle, []);
                    catch
                        % 验证可能会产生错误，这是正常的
                    end
                end
                
                % 检查结果
                new_value = str2double(get(edit_handle, 'String'));
                
                if should_pass && (new_value ~= test_value)
                    validation_errors{end+1} = sprintf('%s正常值验证失败', param_name);
                elseif ~should_pass && (new_value == test_value)
                    validation_errors{end+1} = sprintf('%s异常值未被拒绝', param_name);
                end
                
                % 恢复原值
                set(edit_handle, 'String', old_value);
            end
        end
        
        if isempty(validation_errors)
            result = true;
            msg = '参数验证功能正常';
        else
            result = false;
            msg = strjoin(validation_errors, '; ');
        end
        
    catch ME
        result = false;
        msg = ME.message;
    end
end

function [result, msg] = testFileOperations()
    %% 测试文件操作功能
    
    try
        % 测试参数保存
        test_filename = 'test_params_temp.mat';
        
        % 创建测试参数
        params = struct();
        params.po1 = 1000;
        params.vp1 = 1500;
        params.f0 = 10000;
        
        % 保存测试参数
        save(test_filename, 'params');
        
        % 验证文件是否创建
        if ~exist(test_filename, 'file')
            result = false;
            msg = '参数文件保存失败';
            return;
        end
        
        % 测试参数加载
        loaded_data = load(test_filename);
        
        if ~isfield(loaded_data, 'params')
            result = false;
            msg = '参数文件格式错误';
        else
            % 清理测试文件
            delete(test_filename);
            
            result = true;
            msg = '文件操作功能正常';
        end
        
    catch ME
        result = false;
        msg = ME.message;
        
        % 清理可能残留的测试文件
        if exist('test_params_temp.mat', 'file')
            delete('test_params_temp.mat');
        end
    end
end

function [result, msg] = testResultDisplay()
    %% 测试结果显示功能
    
    try
        % 检查结果显示相关控件
        required_controls = {'result_axes', 'display_popup', 'file_popup'};
        missing_controls = {};
        
        for i = 1:length(required_controls)
            if isempty(findobj('Tag', required_controls{i}))
                missing_controls{end+1} = required_controls{i};
            end
        end
        
        if ~isempty(missing_controls)
            result = false;
            msg = sprintf('缺少结果显示控件: %s', strjoin(missing_controls, ', '));
            return;
        end
        
        % 检查是否有结果文件
        files = dir('FDTD_SeismicLogging_*.mat');
        
        if isempty(files)
            result = true;
            msg = '结果显示控件正常（无结果文件可测试）';
        else
            % 测试文件列表更新
            try
                refreshFileList();
                result = true;
                msg = '结果显示功能正常';
            catch ME
                result = false;
                msg = sprintf('结果显示功能异常: %s', ME.message);
            end
        end
        
    catch ME
        result = false;
        msg = ME.message;
    end
end

function [result, msg] = testInterfaceResponse()
    %% 测试界面响应性能
    
    try
        % 测试界面元素的响应时间
        start_time = tic;
        
        % 查找主要控件
        main_controls = findobj('Type', 'uicontrol');
        
        if length(main_controls) < 10
            result = false;
            msg = '界面控件数量异常';
            return;
        end
        
        % 测试控件访问速度
        for i = 1:min(10, length(main_controls))
            get(main_controls(i), 'String');
        end
        
        elapsed_time = toc(start_time);
        
        if elapsed_time > 1.0
            result = false;
            msg = sprintf('界面响应过慢: %.2f秒', elapsed_time);
        else
            result = true;
            msg = sprintf('界面响应正常: %.3f秒', elapsed_time);
        end
        
    catch ME
        result = false;
        msg = ME.message;
    end
end
