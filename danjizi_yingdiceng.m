%% ========================================================================
%  三维弹性波有限差分时域（FDTD）正演模拟程序
%  3D Elastic Wave Finite-Difference Time-Domain Forward Modeling
clc;clear;close all;
%% ========================================================================
%  第一部分：网格参数设置
%% ========================================================================

nx = 64;    % X方向网格
ny = 64;    % Y方向网格数
nz = 256;    % Z方向网格数

%% ========================================================================
%  第二部分：物理参数和数值参数设置
%% ========================================================================
% 【物理意义】
% 1. 空间参数：定义计算域的物理尺寸，针对声波测井几何特征
% 2. 材料参数：描述地层介质的弹性性质（体积模量、剪切模量、密度）
% 3. 时间参数：确保数值稳定性的CFL条件

% 【计算域物理尺寸】定义三维计算空间的实际尺寸
Lx           =   1.0;   % X方向长度：1米（水平方向）
Ly           =   1.0;   % Y方向长度：1米（水平方向）
Lz           =   5.0;   % Z方向长度：5米（深度方向）

% 【时间步数】定义整个模拟的时间长度
nt           =   1000;  % 时间步数：1500步（总模拟时间 = nt * dt）

% 【网格间距计算】将连续空间离散化为网格点
dx           =   Lx/(nx-1);  % X方向网格间距 = 1.0/63 ≈ 0.0159m
dy           =   Ly/(ny-1);  % Y方向网格间距 = 1.0/63 ≈ 0.0159m  
dz           =   Lz/(nz-1);  % Z方向网格间距 = 5.0/255 ≈ 0.0196m

% 【声波测井标准材料参数】根据实际地层和井筒物理特性设置
% 参考标准声波测井参数表，区分井筒内流体和地层岩石

% 井筒内（流体）参数
vp_fluid     =   1500;      % P波速度：1500 m/s（泥浆）
vs_fluid     =   0;         % S波速度：0 m/s（流体无剪切强度）
rho_fluid    =   1000;      % 密度：1000 kg/m³

% 地层（快速地层）参数
vp_rock      =   4000;      % P波速度：4000 m/s（快速地层）
vs_rock      =   2300;      % S波速度：2300 m/s
rho_rock     =   2500;      % 密度：2500 kg/m³

% 根据波速和密度计算弹性模量
% 井筒内（流体）弹性常数
G_fluid      =   rho_fluid * vs_fluid^2;                    % 剪切模量：G = ρ*vs² = 0
K_fluid      =   rho_fluid * vp_fluid^2 - 4*G_fluid/3;     % 体积模量：K = ρ*vp² - 4G/3

% 地层（岩石）弹性常数
G_rock       =   rho_rock * vs_rock^2;                      % 剪切模量：G = ρ*vs²
K_rock       =   rho_rock * vp_rock^2 - 4*G_rock/3;        % 体积模量：K = ρ*vp² - 4G/3

% 【井筒几何参数】定义圆柱形井筒
well_radius  =   0.1;       % 井筒半径：0.1m（标准井径）
well_center_x =  Lx / 2;    % 井筒中心X坐标：0.5m
well_center_y =  Ly / 2;    % 井筒中心Y坐标：0.5m

% 【材料参数场初始化】根据位置分配不同材料参数
K            =   zeros(nx, ny, nz);    % 体积模量场
G            =   zeros(nx, ny, nz);    % 剪切模量场
rho          =   zeros(nx, ny, nz);    % 密度场

% 【分区材料参数赋值】根据距井轴距离判断材料类型
for i = 1:nx
    for j = 1:ny
        % 计算当前网格点的物理坐标
        x = (i-1) * dx;
        y = (j-1) * dy;
        
        % 计算到井轴的距离
        distance_to_well = sqrt((x - well_center_x)^2 + (y - well_center_y)^2);
        
        if distance_to_well <= well_radius
            % 井筒内：使用流体参数
            K(i, j, :) = K_fluid;
            G(i, j, :) = G_fluid;
            rho(i, j, :) = rho_fluid;
        else
            % 地层：使用岩石参数
            K(i, j, :) = K_rock;
            G(i, j, :) = G_rock;
            rho(i, j, :) = rho_rock;
        end
    end
end

% 【时间步长计算】基于最大波速的CFL稳定性条件
cfl          =   0.6;       % CFL数：提高到0.6以减少数值频散
vp_max       =   max(vp_rock, vp_fluid);  % 最大P波速度
dt           =   min([dx, dy, dz]) / vp_max * cfl;  % 时间步长计算（秒）

% 【参数验证输出】显示关键物理参数
fprintf('=== 声波测井物理参数设置 ===\n');
fprintf('井筒内流体：vp=%.0f m/s, vs=%.0f m/s, ρ=%.0f kg/m³\n', vp_fluid, vs_fluid, rho_fluid);
fprintf('地层岩石：vp=%.0f m/s, vs=%.0f m/s, ρ=%.0f kg/m³\n', vp_rock, vs_rock, rho_rock);
fprintf('井筒半径：%.1f m, 时间步长：%.2e s\n', well_radius, dt);

% 【PML边界权重初始化】为完美匹配层吸收边界条件做准备
% 初始时所有权重设为1=无衰减，后续在边界区域修改为衰减因子，用权重的设置来控制波的衰减，<1 = 有衰减：波幅度减小
weights_x    =   ones(nx+1,1);  % X方向PML衰减权重：初始化为全1数组
weights_y    =   ones(ny+1,1);  % Y方向PML衰减权重：初始化为全1数组
weights_z    =   ones(nz+1,1);  % Z方向PML衰减权重：初始化为全1数组

% 【震源位置设置】定义地震波激发点的物理坐标和网格位置
% 物理坐标定义（单位：米）
source_x     =   Lx / 2;        % X方向震源物理坐标：0.5m（计算域中心）
source_y     =   Ly / 2;        % Y方向震源物理坐标：2.5m（计算域中心）
source_z     =   1.0;           % Z方向震源物理坐标：1.0m（井底位置）

% 物理坐标转换为网格索引
isx          =   round(source_x / dx) + 1;  % X方向震源网格索引
isy          =   round(source_y / dy) + 1;  % Y方向震源网格索引
isz          =   round(source_z / dz) + 1;  % Z方向震源网格索引

% 【检波器设置】定义检波器位置和波形记录
% 检波器物理坐标（震源上方2米）
receiver_x   =   source_x;       % X坐标与震源相同：0.5m
receiver_y   =   source_y;       % Y坐标与震源相同：0.5m  
receiver_z   =   source_z + 2.0; % Z坐标在震源上方2米：3.0m

% 检波器网格索引
irx          =   round(receiver_x / dx) + 1;  % X方向检波器网格索引
iry          =   round(receiver_y / dy) + 1;  % Y方向检波器网格索引
irz          =   round(receiver_z / dz) + 1;  % Z方向检波器网格索引

% 波形记录数组初始化
receiver_data = zeros(nt, 1);    % 存储检波器波形数据
time_axis    = (0:nt-1) * dt;    % 时间轴（秒）

% 检查检波器位置是否在计算域内
if irx < 1 || irx > nx || iry < 1 || iry > ny || irz < 1 || irz > nz
    warning('检波器位置超出计算域范围！');
    fprintf('检波器位置：(%.2f, %.2f, %.2f)m\n', receiver_x, receiver_y, receiver_z);
    fprintf('检波器索引：(%d, %d, %d)\n', irx, iry, irz);
else
    fprintf('检波器设置成功：物理位置(%.2f, %.2f, %.2f)m，网格索引(%d, %d, %d)\n', ...
            receiver_x, receiver_y, receiver_z, irx, iry, irz);
end

% 创建实时波形显示窗口
figure('Name', '声波测井实时波形', 'Position', [100 100 800 500]);
h_waveform = plot(time_axis*1e6, zeros(nt,1), 'b-', 'LineWidth', 1.5);
xlabel('时间 (μs)');
ylabel('压力');
title('检波器实时波形（震源上方2m）');
grid on;
xlim([0 max(time_axis)*1e6]);

%% ========================================================================
%  第三部分：交错网格的材料参数插值
%% ========================================================================
% 【物理意义】
% 交错网格中，速度分量和应力分量定义在不同的网格位置：
% - 正应力（σxx,σyy,σzz）定义在网格中心
% - 剪切应力（σxy,σxz,σyz）定义在网格边中点
% - 速度分量（vx,vy,vz）定义在网格面中心
% 因此需要将材料参数插值到相应的网格位置

% 剪切模量插值 - 匹配剪切应力分量位置
% 【物理意义】剪切应力需要在其定义位置使用适当的剪切模量值
% 采用调和平均：1/G_avg = 1/4 * ∑(1/G_i) （四个相邻网格点）

% tau_xy位置: (nx+1, ny+1, nz) -> 内部更新区域 (2:nx, 2:ny, :)
G_tau_xy     =   zeros(nx+1, ny+1, nz);
G_tau_xy(2:end-1,2:end-1,:) = (1/4*(1./G(1:end-1,1:end-1,:) + 1./G(2:end,2:end,:) + 1./G(2:end,1:end-1,:) + 1./G(1:end-1,2:end,:)) ).^(-1);
% tau_xz位置: (nx+1, ny, nz+1) -> 内部更新区域 (2:nx, :, 2:nz)
G_tau_xz     =   zeros(nx+1, ny, nz+1);
G_tau_xz(2:end-1,:,2:end-1) = (1/4*(1./G(1:end-1,:,1:end-1) + 1./G(2:end,:,2:end) + 1./G(2:end,:,1:end-1) + 1./G(1:end-1,:,2:end)) ).^(-1);
% tau_yz位置: (nx, ny+1, nz+1) -> 内部更新区域 (:, 2:ny, 2:nz)
G_tau_yz     =   zeros(nx, ny+1, nz+1);
G_tau_yz(:,2:end-1,2:end-1) = (1/4*(1./G(:,1:end-1,1:end-1) + 1./G(:,2:end,2:end) + 1./G(:,2:end,1:end-1) + 1./G(:,1:end-1,2:end)) ).^(-1);

% 交错网格的密度插值 - 匹配速度场维度
% 【物理意义】速度更新方程需要在速度分量的定义位置使用密度值
% 采用算术平均：ρ_avg = 1/2 * (ρ_i + ρ_j) （两个相邻网格点）

rho_x        =    zeros(nx+1, ny, nz);   % Vx位置的密度 (nx+1, ny, nz)
rho_y        =    zeros(nx, ny+1, nz);   % Vy位置的密度 (nx, ny+1, nz)
rho_z        =    zeros(nx, ny, nz+1);   % Vz位置的密度 (nx, ny, nz+1)

% Vx位置的密度 (nx+1, ny, nz) - X方向面中心
rho_x(2:end-1,:,:) = 0.5 .* (rho(1:end-1,:,:) + rho(2:end,:,:));
rho_x(1,:,:) = rho(1,:,:);      % 左边界处理
rho_x(end,:,:) = rho(end,:,:);  % 右边界处理

% Vy位置的密度 (nx, ny+1, nz) - Y方向面中心
rho_y(:,2:end-1,:) = 0.5 .* (rho(:,1:end-1,:) + rho(:,2:end,:));
rho_y(:,1,:) = rho(:,1,:);      % 前边界处理
rho_y(:,end,:) = rho(:,end,:);  % 后边界处理

% Vz位置的密度 (nx, ny, nz+1) - Z方向面中心
rho_z(:,:,2:end-1) = 0.5 .* (rho(:,:,1:end-1) + rho(:,:,2:end));
rho_z(:,:,1) = rho(:,:,1);      % 下边界处理
rho_z(:,:,end) = rho(:,:,end);  % 上边界处理
% 批量保存模型参数到二进制文件
fileList = {'K.dat', 'G.dat', 'rho.dat'};
varList  = {K,       G,       rho     };
for i = 1:3
    fid = fopen(fileList{i},'wb'); fwrite(fid, varList{i}(:), 'double'); fclose(fid);
end

% 保存核心参数
parameter_1 = [dx dy dz dt isx isy isz];  % 核心参数数组：网格间距、时间步长、震源位置
fid = fopen('parameter_1.dat','wb'); fwrite(fid, parameter_1, 'double'); fclose(fid);

%% ========================================================================
%  第四部分：场变量初始化（交错网格定义）
%% ========================================================================
% 【物理意义】
% 交错网格中，不同的物理量定义在不同的网格位置：
% 1. 速度分量：定义在网格面的中心，保证速度连续性
% 2. 正应力：定义在网格体的中心，保证应力平衡
% 3. 剪切应力：定义在网格边的中点，保证剪切变形连续性
% 这种安排保证了数值稳定性和物理意义的正确性

% 速度分量初始化（定义在网格面中心）
Vx           =    zeros(nx+1, ny, nz);   % X方向速度分量 (YZ面中心)
Vy           =    zeros(nx, ny+1, nz);   % Y方向速度分量 (XZ面中心)
Vz           =    zeros(nx, ny, nz+1);   % Z方向速度分量 (XY面中心)

% 正应力分量初始化（定义在网格体中心）
tau_xx       =    zeros(nx, ny, nz);     % X方向正应力分量
tau_yy       =    zeros(nx, ny, nz);     % Y方向正应力分量
tau_zz       =    zeros(nx, ny, nz);     % Z方向正应力分量

% 剪切应力分量初始化（定义在网格边中点）
tau_xy       =    zeros(nx+1, ny+1, nz); % XY平面剪切应力分量
tau_xz       =    zeros(nx+1, ny, nz+1); % XZ平面剪切应力分量
tau_yz       =    zeros(nx, ny+1, nz+1); % YZ平面剪切应力分量

% 压力场初始化（等价于各向同性介质中的体积应力）
Pr           =    zeros(nx, ny, nz);     % 压力场（定义在网格体中心） 
%% ========================================================================
%  第五部分：完美匹配层（PML）边界条件设置
%% ========================================================================

lpml         =    15;   % PML层厚度（网格点数）

%% ========================================================================
%  第六部分：震源参数设置
%% ========================================================================

% 【震源频率参数】定义Ricker子波的频率特性
f0           =    8000;     % 震源主频率：10kHz（声波测井标准频率）
t0           =    1.0/f0;   % 震源延迟时间：1e-4秒（避免初始时刻的数值问题）

% 【PML衰减系数数组初始化】为各方向的PML边界条件做准备
% 这些数组将存储每个网格点的PML衰减系数σ
pml_x        =    zeros(nx, 1);  % X方向PML衰减系数数组
pml_y        =    zeros(ny, 1);  % Y方向PML衰减系数数组
pml_z        =    zeros(nz, 1);  % Z方向PML衰减系数数组
pml_width    =    lpml;          % PML层宽度


% 【PML衰减系数计算循环】计算每个边界点的衰减系数

for i = 1:lpml              % 遍历PML层内的每个网格点
    % 【反射系数】定义PML边界的理论反射系数
    R = 1e-9;               % 反射系数：10^-9（极小值，表示几乎完全吸收）
    
    % 【衰减系数公式】基于Berenger的PML理论公式
    % 公式：σ(d) = (3/2) * (vmax/dx) * log(1/R) * (d/δ)^2
    % 其中：vmax是最大波速，dx是网格间距，d是到边界的距离，δ是PML层厚度
    % 二次函数形式保证了平滑过渡和有效吸收
    
    % 计算最大P波速度
    vmax = max(sqrt((K + 4/3*G)./rho), [], 'all');  % P波速度最大值
    
    % 距离边界的归一化距离（从边界到内部：1→0）
    d_normalized = (lpml - i + 1) / lpml;
    
    % 正确的PML衰减系数公式
    sigma(i) = (3/2) * (vmax/dx) * log(1/R) * d_normalized^2;
    
    % 【对称设置】在计算域的两端设置相同的衰减系数
    % 物理意义：确保左右、前后、上下边界具有相同的吸收特性
    pml_x(i) = sigma(i);        % 左边界（i=1处）的X方向衰减系数
    pml_x(nx-i+1) = sigma(i);   % 右边界（i=nx处）的X方向衰减系数

    pml_y(i) = sigma(i);        % 前边界（j=1处）的Y方向衰减系数
    pml_y(ny-i+1) = sigma(i);   % 后边界（j=ny处）的Y方向衰减系数

    pml_z(i) = sigma(i);        % 上边界（k=1处）的Z方向衰减系数
    pml_z(nz-i+1) = sigma(i);   % 下边界（k=nz处）的Z方向衰减系数
end

% 【PML衰减权重计算循环】将衰减系数转换为实际的衰减权重
% 【物理意义】衰减权重 w = exp(-σ*dt) 是每个时间步应用的实际衰减因子
% 指数形式保证了衰减的物理合理性和数值稳定性
for i = 1:lpml              % 遍历PML层内的每个网格点
    % 【X方向衰减权重计算】
    % 公式：w = exp(-σ*Δt)，其中σ是衰减系数，Δt是时间步长
    damping_factor_x = exp(-pml_x(i) * dt);     % 计算X方向衰减因子
    weights_x(i) = damping_factor_x;            % 左边界X方向衰减权重
    
    % 【Y方向衰减权重计算】
    damping_factor_y = exp(-pml_y(i) * dt);     % 计算Y方向衰减因子
    weights_y(i) = damping_factor_y;            % 前边界Y方向衰减权重
    
    % 【Z方向衰减权重计算】
    damping_factor_z = exp(-pml_z(i) * dt);     % 计算Z方向衰减因子
    weights_z(i) = damping_factor_z;            % 上边界Z方向衰减权重
    
    % 【对称边界设置】为另一侧边界设置相同的衰减权重
    weights_x(nx-i+1) = damping_factor_x;       % 右边界X方向衰减权重
    weights_y(ny-i+1) = damping_factor_y;       % 后边界Y方向衰减权重
    weights_z(nz-i+1) = damping_factor_z;       % 下边界Z方向衰减权重
end

% 【交错网格位置的PML权重计算】
% 【物理意义】在交错网格中，应力分量定义在网格中心或边中点
% 需要将速度点的PML权重插值到应力点位置
weights_xP    =     0.5*(weights_x(1:end-1)+weights_x(2:end));  % X方向应力点PML权重
weights_yP    =     0.5*(weights_y(1:end-1)+weights_y(2:end));  % Y方向应力点PML权重
weights_zP    =     0.5*(weights_z(1:end-1)+weights_z(2:end));  % Z方向应力点PML权重



% 【数组维度重整】为了在三维计算中正确应用，需要重整数组维度
% 物理意义：确保数组维度与相应的空间方向一致
weights_yP    =     reshape(weights_yP,1,ny);      % 将Y方向应力点权重重整为行向量
weights_zP    =     reshape(weights_zP,1,1,nz);    % 将Z方向应力点权重重整为第三维向量
weights_y     =     reshape(weights_y,1,ny+1);     % 将Y方向速度点权重重整为行向量
weights_z     =     reshape(weights_z,1,1,nz+1);   % 将Z方向速度点权重重整为第三维向量
% 【Ricker子波震源生成】
% 【物理意义】Ricker子波是地震勘探中最常用的震源函数之一
% 特点：1) 时域对称，频域单极性  2) 无直流分量  3) 能量集中在主频附近
src           =     zeros(nt,1);            % 初始化震源时间序列数组

% 【Ricker子波公式计算循环】
% Ricker子波公式：s(t) = (1 - 2π²f₀²t²) * exp(-π²f₀²t²)
% 其中：f₀是主频率，t是相对于延迟时间t₀的时间
for it = 1:nt                           % 遍历所有时间步
    % 【时间计算】相对于震源延迟时间的实际时间
    t  = it * dt - t0;                  % 实际时间 = 当前时间步 * 时间步长 - 延迟时间
    
    % 【Ricker子波公式】计算当前时刻的震源振幅
    % 第一项：(1 - 2π²f₀²t²) 控制子波的对称性和主极性
    % 第二项：exp(-π²f₀²t²) 控制子波的包络和衰减
    src(it) = (1 - 2 * pi^2 * f0^2 * t^2) * exp(-pi^2 * f0^2 * t^2);
end

% 【震源数据保存】保存震源时间序列到文件供后续分析
fid           =     fopen('src.dat','wb'); fwrite(fid,src(:),'double'); fclose(fid);  % 保存震源数据


%% ========================================================================
%  第七部分：主时间循环 - 三维弹性波方程求解
%% ========================================================================
% 【物理机制核心】
% 这里实现了三维弹性波动方程的数值求解，包括两个基本方程：
% 1. 运动方程（牛顿第二定律）：ρ ∂v/∂t = ∇·σ + f
% 2. 本构方程（广义胡克定律）：∂σ/∂t = C : ∇v
%
% 时间步进策略：
% 1. 首先更新应力场（基于当前速度场）
% 2. 然后更新速度场（基于新的应力场）
% 3. 最后应用PML衰减（吸收边界反射）

for it = 1:nt  % 主时间循环
    it;  % 显示当前时间步
    
    %% ----------------------------------------------------------------
    %  步骤 1：震源加载
    %% ----------------------------------------------------------------
    % 【物理意义】在指定位置加载Ricker子波震源
    % 选择Vz分量加载：模拟垂直方向的点源激发
    Vz(isx, isy, isz) = Vz(isx, isy, isz) + src(it);
    
    %% ----------------------------------------------------------------
    %  步骤 1.1：检波器数据采集
    %% ----------------------------------------------------------------
    % 【物理意义】在检波器位置记录压力场数据
    if irx >= 1 && irx <= nx && iry >= 1 && iry <= ny && irz >= 1 && irz <= nz
        % 记录压力场数据（声波测井主要记录压力响应）
        receiver_data(it) = Pr(irx, iry, irz);
    end
    
    %% ----------------------------------------------------------------
    %  步骤 1.2：实时波形显示更新
    %% ----------------------------------------------------------------
    % 每隔10个时间步更新一次显示，减少计算负担
    if mod(it, 10) == 0 || it == nt
        % 更新检波器波形
        set(h_waveform, 'YData', receiver_data);
        
        % 动态调整Y轴范围
        if max(abs(receiver_data(1:it))) > 0
            ylim([-1.2*max(abs(receiver_data(1:it))), 1.2*max(abs(receiver_data(1:it)))]);
        end
        
        % 更新图形显示
        drawnow;
        
        % 显示进度信息
        if mod(it, 50) == 0
            fprintf('时间步: %d/%d (%.1f%%), 检波器当前值: %.2e\n', ...
                    it, nt, 100*it/nt, receiver_data(it));
        end
    end
    
    % 每10个时间步显示一次进度（不依赖图形更新）
    if mod(it, 10) == 0
        fprintf('.');
        if mod(it, 100) == 0
            fprintf(' [%d/%d]\n', it, nt);
        end
    end
    
    %% ----------------------------------------------------------------
    %  步骤 2：应力场更新（本构方程）
    %% ----------------------------------------------------------------
    % 【物理意义】根据广义胡克定律，应力变化率与应变率成正比
    % 对于各向同性弹性介质：
    % σij = λδij∇·v + 2μεij
    % 其中：λ = K - 2μ/3，μ = G，εij = 1/2(∂vi/∂xj + ∂vj/∂xi)
    
    % 计算速度散度（体积应变率）
    divV = diff(Vx,1,1)/dx + diff(Vy,1,2)/dy + diff(Vz,1,3)/dz;
    
    % 更新压力场（体积应力）
    % 【物理意义】P = -K·∇·v（体积模量×体积应变率）
    % 压力场是体积应力的表示形式，描述了介质的体积变化
    Pr = (Pr - K.*divV*dt);
    
    % 更新正应力分量（偏应力 + 静水压力）
    % 【物理意义】正应力由两部分组成：偏应力 + 静水压力
    % 偏应力：τij = 2G(εij - 1/3δijεkk)，描述形状改变
    % 静水压力：P = -K∇·v，描述体积改变
    % 最终：σxx = τxx + P = 2G(∂vx/∂x - 1/3∇·v) + P
    tau_xx = (tau_xx + 2*G.*dt.*( diff(Vx,1,1)/dx - 1/3*divV ));  % X方向正应力更新
    tau_yy = (tau_yy + 2*G.*dt.*( diff(Vy,1,2)/dy - 1/3*divV ));  % Y方向正应力更新
    tau_zz = (tau_zz + 2*G.*dt.*( diff(Vz,1,3)/dz - 1/3*divV ));  % Z方向正应力更新
    
    % 更新剪切应力分量（基于剪切应变率）
    % 【物理意义】剪切应力与剪切应变率成正比：σij = Gγij = G(∂vi/∂xj + ∂vj/∂xi)
    % 剪切应变率描述了介质在不同方向上的形状改变程度
    tau_xy(2:end-1,2:end-1,:) = (tau_xy(2:end-1,2:end-1,:) + G_tau_xy(2:end-1,2:end-1,:).*dt.*( diff(Vy(:, 2:end-1, :),1,1)/dx + diff(Vx(2:end-1, :, :),1,2)/dy ));  % XY平面剪切应力
    tau_xz(2:end-1,:,2:end-1) = (tau_xz(2:end-1,:,2:end-1) + G_tau_xz(2:end-1,:,2:end-1).*dt.*( diff(Vz(:, :, 2:end-1),1,1)/dx + diff(Vx(2:end-1, :, :),1,3)/dz ));  % XZ平面剪切应力
    tau_yz(:,2:end-1,2:end-1) = (tau_yz(:,2:end-1,2:end-1) + G_tau_yz(:,2:end-1,2:end-1).*dt.*( diff(Vy(:, 2:end-1, :),1,3)/dz + diff(Vz(:, :, 2:end-1),1,2)/dy ));  % YZ平面剪切应力
    %% ----------------------------------------------------------------
    %  步骤 2.1：Z方向PML衰减应用（对应力场）
    %% ----------------------------------------------------------------
    % 【物理意义】在Z方向边界区域对应力分量应用PML衰减
    % PML衰减原理：通过乘以衰减因子 w = exp(-σ*dt) 来吸收波能量
    % 边界附近的衰减系数越大，吸收效果越强
    for i = 1:nx                    % 遍历所有X方向网格点
        for j=1:ny                  % 遍历所有Y方向网格点
            % 对Z方向的所有应力分量应用Z方向PML衰减权重
            Pr(i,j,:) = Pr(i,j,:) .*weights_zP;                    % 压力场的Z方向PML衰减
            tau_xx(i,j,:) = tau_xx(i,j,:) .*weights_zP;            % X方向正应力的Z方向PML衰减
            tau_yy(i,j,:) = tau_yy(i,j,:) .*weights_zP;            % Y方向正应力的Z方向PML衰减
            tau_zz(i,j,:) = tau_zz(i,j,:) .*weights_zP;            % Z方向正应力的Z方向PML衰减
            tau_xy(i,j,:) = tau_xy(i,j,:) .*weights_zP;            % XY剪切应力的Z方向PML衰减
            tau_xz(i,j,2:end-1) = tau_xz(i,j,2:end-1) .*weights_zP(2:end);  % XZ剪切应力的Z方向PML衰减（只对内部点）
            tau_yz(i,j,2:end-1) = tau_yz(i,j,2:end-1) .*weights_zP(2:end);  % YZ剪切应力的Z方向PML衰减（只对内部点）
        end
    end
    
    %% ----------------------------------------------------------------
    %  步骤 2.2：Y方向PML衰减应用（对应力场）
    %% ----------------------------------------------------------------
    % 【物理意义】在Y方向边界区域对应力分量应用PML衰减
    % 注意：这里使用weights_yP，对应于应力分量在交错网格上的位置
    for i = 1:nx                    % 遍历所有X方向网格点
        for k = 1:nz                % 遍历所有Z方向网格点
            % 对Y方向的所有应力分量应用Y方向PML衰减权重
            Pr(i,:,k) = Pr(i,:,k) .*weights_yP;                    % 压力场的Y方向PML衰减
            tau_xx(i,:,k) = tau_xx(i,:,k) .*weights_yP;            % X方向正应力的Y方向PML衰减
            tau_yy(i,:,k) = tau_yy(i,:,k) .*weights_yP;            % Y方向正应力的Y方向PML衰减
            tau_zz(i,:,k) = tau_zz(i,:,k) .*weights_yP;            % Z方向正应力的Y方向PML衰减
            tau_xy(i,2:end-1,k) = tau_xy(i,2:end-1,k) .*weights_yP(2:end);  % XY剪切应力的Y方向PML衰减（只对内部点）
            tau_xz(i,:,k) = tau_xz(i,:,k) .*weights_yP;            % XZ剪切应力的Y方向PML衰减
            tau_yz(i,2:end-1,k) = tau_yz(i,2:end-1,k) .*weights_yP(2:end);  % YZ剪切应力的Y方向PML衰减（只对内部点）
        end
    end
    %% ----------------------------------------------------------------
    %  步骤 2.3：X方向PML衰减应用（对应力场）
    %% ----------------------------------------------------------------
    % 【物理意义】在X方向边界区域对应力分量应用PML衰减
    % 注意：这里使用weights_xP，对应于应力分量在交错网格上的位置
    for j = 1:ny                    % 遍历所有Y方向网格点
        for k = 1:nz                % 遍历所有Z方向网格点
            % 对X方向的所有应力分量应用X方向PML衰减权重
            Pr(:,j,k) = Pr(:,j,k) .*weights_xP;                    % 压力场的X方向PML衰减
            tau_xx(:,j,k) = tau_xx(:,j,k) .*weights_xP;            % X方向正应力的X方向PML衰减
            tau_yy(:,j,k) = tau_yy(:,j,k) .*weights_xP;            % Y方向正应力的X方向PML衰减
            tau_zz(:,j,k) = tau_zz(:,j,k) .*weights_xP;            % Z方向正应力的X方向PML衰减
            tau_xy(2:end-1,j,k) = tau_xy(2:end-1,j,k) .*weights_xP(2:end);  % XY剪切应力的X方向PML衰减（只对内部点）
            tau_xz(2:end-1,j,k) = tau_xz(2:end-1,j,k) .*weights_xP(2:end);  % XZ剪切应力的X方向PML衰减（只对内部点）
            tau_yz(:,j,k) = tau_yz(:,j,k) .*weights_xP;            % YZ剪切应力的X方向PML衰减
        end
    end

    %% ----------------------------------------------------------------
    %  步骤 3：速度场更新（运动方程）
    %% ----------------------------------------------------------------
    % 【物理意义】根据牛顿第二定律，加速度与合力成正比
    % 运动方程：ρ ∂v/∂t = ∇·σ
    % 具体形式：
    % ρ ∂vx/∂t = ∂σxx/∂x + ∂σxy/∂y + ∂σxz/∂z
    % ρ ∂vy/∂t = ∂σxy/∂x + ∂σyy/∂y + ∂σyz/∂z
    % ρ ∂vz/∂t = ∂σxz/∂x + ∂σyz/∂y + ∂σzz/∂z
    
    % 【X方向速度分量更新】基于牛顿第二定律的运动方程
    % 运动方程：ρ ∂vx/∂t = ∂σxx/∂x + ∂σxy/∂y + ∂σxz/∂z
    % 物理意义：质点的加速度等于作用在其上的合力除以质量
    Vx(2:end-1,2:end-1,2:end-1) = Vx(2:end-1,2:end-1,2:end-1) + dt./rho_x(2:end-1,2:end-1,2:end-1) .* ( diff(tau_xx(:,2:end-1,2:end-1) - Pr(:,2:end-1,2:end-1),1,1)/dx + diff(tau_xy(2:end-1,2:end-1,2:end-1),1,2)/dy + diff(tau_xz(2:end-1,2:end-1,2:end-1),1,3)/dz );
    
    % 【Y方向速度分量更新】基于牛顿第二定律的运动方程
    % 运动方程：ρ ∂vy/∂t = ∂σxy/∂x + ∂σyy/∂y + ∂σyz/∂z
    % 物理意义：Y方向的加速度由三个方向上的应力梯度决定
    Vy(2:end-1,2:end-1,2:end-1) = Vy(2:end-1,2:end-1,2:end-1) + dt./rho_y(2:end-1,2:end-1,2:end-1) .* ( diff(tau_xy(2:end-1,2:end-1,2:end-1),1,1)/dx + diff(tau_yy(2:end-1,:,2:end-1) - Pr(2:end-1,:,2:end-1),1,2)/dy + diff(tau_yz(2:end-1,2:end-1,2:end-1),1,3)/dz );
    
    % 【Z方向速度分量更新】基于牛顿第二定律的运动方程
    % 运动方程：ρ ∂vz/∂t = ∂σxz/∂x + ∂σyz/∂y + ∂σzz/∂z
    % 物理意义：Z方向的加速度由三个方向上的应力梯度决定，包括重力效应
    Vz(2:end-1,2:end-1,2:end-1) = Vz(2:end-1,2:end-1,2:end-1) + dt./rho_z(2:end-1,2:end-1,2:end-1) .* ( diff(tau_xz(2:end-1,2:end-1,2:end-1),1,1)/dx + diff(tau_yz(2:end-1,2:end-1,2:end-1),1,2)/dy + diff(tau_zz(2:end-1,2:end-1,:) - Pr(2:end-1,2:end-1,:),1,3)/dz );
    %% ----------------------------------------------------------------
    %  步骤 4：PML衰减应用（吸收边界条件）
    %% ----------------------------------------------------------------
    % 【物理意义】PML层通过对场变量乘以衰减因子来实现波的吸收
    % 衰减因子计算：w = exp(-σ*dt)，其中σ为衰减系数
    % 边界附近的衰减系数较大，远离边界的衰减系数较小
    % 这样可以在不影响内部波场的情况下，有效吸收达到边界的波
    
    %% ----------------------------------------------------------------
    %  步骤 4.1：X方向速度分量PML衰减应用
    %% ----------------------------------------------------------------
    % 【物理意义】对X方向速度分量在X方向边界区域应用PML衰减
    % 速度场的PML衰减确保波在到达边界时被有效吸收，防止人工反射
    % 使用weights_x权重，对应于Vx分量在交错网格上的X方向位置
    for j = 1:ny                    % 遍历所有Y方向网格点
        for k = 1:nz                % 遍历所有Z方向网格点
            % 对X方向速度分量的内部点应用X方向PML衰减权重
            % 只对内部点(2:end-1)应用，边界点保持边界条件
            Vx(2:end-1,j,k) = Vx(2:end-1,j,k) .*weights_x(2:end-1);
        end
    end
    
    %% ----------------------------------------------------------------
    %  步骤 4.2：Y方向速度分量PML衰减应用
    %% ----------------------------------------------------------------
    % 【物理意义】对Y方向速度分量在Y方向边界区域应用PML衰减
    % 确保Y方向传播的波在Y边界处被有效吸收
    % 使用weights_y权重，对应于Vy分量在交错网格上的Y方向位置
    for i = 1:nx                    % 遍历所有X方向网格点
        for k = 1:nz                % 遍历所有Z方向网格点
            % 对Y方向速度分量的内部点应用Y方向PML衰减权重
            % 只对内部点(2:end-1)应用，保持边界条件的一致性
            Vy(i,2:end-1,k) = Vy(i,2:end-1,k) .*weights_y(2:end-1);
        end
    end
    
    %% ----------------------------------------------------------------
    %  步骤 4.3：Z方向速度分量PML衰减应用
    %% ----------------------------------------------------------------
    % 【物理意义】对Z方向速度分量在Z方向边界区域应用PML衰减
    % 特别重要：Z方向通常对应深度方向，需要有效吸收向下传播的波
    % 使用weights_z权重，对应于Vz分量在交错网格上的Z方向位置
    for i = 1:nx                    % 遍历所有X方向网格点
        for j = 1:ny                % 遍历所有Y方向网格点
            % 对Z方向速度分量的内部点应用Z方向PML衰减权重
            % 只对内部点(2:end-1)应用，确保数值稳定性
            Vz(i,j,2:end-1) = Vz(i,j,2:end-1) .*weights_z(2:end-1);
        end
    end
    
end  % 主时间循环结束

%% ========================================================================
%  第八部分：波形分析和数据保存
%% ========================================================================

% 【最终波形显示】
fprintf('\n=== 声波测井FDTD模拟完成 ===\n');
fprintf('总时间步数：%d\n', nt);
fprintf('模拟总时长：%.2f μs\n', max(time_axis)*1e6);
fprintf('检波器位置：(%.2f, %.2f, %.2f)m\n', receiver_x, receiver_y, receiver_z);
fprintf('震源位置：(%.2f, %.2f, %.2f)m\n', source_x, source_y, source_z);
fprintf('源距：%.2f m\n', abs(receiver_z - source_z));

% 【波形统计分析】
max_amplitude = max(abs(receiver_data));
first_arrival_idx = find(abs(receiver_data) > 0.01*max_amplitude, 1, 'first');
if ~isempty(first_arrival_idx)
    first_arrival_time = time_axis(first_arrival_idx) * 1e6;  % 转换为微秒
    fprintf('最大振幅：%.2e\n', max_amplitude);
    fprintf('初至时间：%.2f μs\n', first_arrival_time);
else
    fprintf('未检测到明显的波形信号\n');
end

% 【创建最终波形图】
figure('Name', '声波测井最终波形', 'Position', [200 200 800 500]);
plot(time_axis*1e6, receiver_data, 'b-', 'LineWidth', 1.5);
xlabel('时间 (μs)');
ylabel('压力');
title(sprintf('检波器最终波形 - 震源上方%.1fm', abs(receiver_z - source_z)));
grid on;
xlim([0 max(time_axis)*1e6]);

% 【数据保存】
save_filename = 'acoustic_logging_3D_results.mat';
save(save_filename, 'receiver_data', 'source_function', 'time_axis', ...
     'receiver_x', 'receiver_y', 'receiver_z', ...
     'source_x', 'source_y', 'source_z', ...
     'dt', 'dx', 'dy', 'dz', 'nt', 'nx', 'ny', 'nz', ...
     'vp_fluid', 'vs_fluid', 'rho_fluid', ...
     'vp_rock', 'vs_rock', 'rho_rock', ...
     'well_radius', 'f0', 't0');

fprintf('\n数据已保存到：%s\n', save_filename);
fprintf('包含内容：波形数据、震源函数、时间轴、几何参数、物理参数\n');

%% ========================================================================
%  程序结束
%% ========================================================================