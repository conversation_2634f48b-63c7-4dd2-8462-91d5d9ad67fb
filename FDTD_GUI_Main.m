function FDTD_GUI_Main()
%% 二维FDTD声波测井正演模拟可视化界面
% 功能：为二维FDTD声波测井程序提供图形化用户界面
% 适用于专利申请和技术演示
% 作者：[您的姓名]
% 版本：1.0
% 日期：2025年

    % 创建主窗口
    fig = figure('Name', '二维FDTD声波测井正演模拟系统', ...
                 'Position', [100, 100, 1200, 800], ...
                 'MenuBar', 'none', ...
                 'ToolBar', 'none', ...
                 'Resize', 'on', ...
                 'NumberTitle', 'off');
    
    % 设置窗口图标和样式
    set(fig, 'Color', [0.94, 0.94, 0.94]);
    
    % 创建主要UI组件
    createMainInterface(fig);
    
    % 使窗口居中显示
    movegui(fig, 'center');
end

function createMainInterface(fig)
    %% 创建主界面布局
    
    % 创建标题
    title_panel = uipanel('Parent', fig, ...
                         'Position', [0.02, 0.92, 0.96, 0.06], ...
                         'BackgroundColor', [0.2, 0.3, 0.5], ...
                         'BorderType', 'none');
    
    uicontrol('Parent', title_panel, ...
              'Style', 'text', ...
              'String', '二维FDTD声波测井正演模拟系统 v1.0', ...
              'Position', [10, 5, 600, 35], ...
              'FontSize', 16, ...
              'FontWeight', 'bold', ...
              'ForegroundColor', 'white', ...
              'BackgroundColor', [0.2, 0.3, 0.5], ...
              'HorizontalAlignment', 'left');
    
    % 创建参数设置面板
    param_panel = uipanel('Parent', fig, ...
                         'Title', '参数设置', ...
                         'Position', [0.02, 0.02, 0.48, 0.88], ...
                         'FontSize', 12, ...
                         'FontWeight', 'bold');
    
    % 创建结果显示面板
    result_panel = uipanel('Parent', fig, ...
                          'Title', '结果显示与控制', ...
                          'Position', [0.52, 0.02, 0.46, 0.88], ...
                          'FontSize', 12, ...
                          'FontWeight', 'bold');
    
    % 创建参数设置界面
    createParameterInterface(param_panel);
    
    % 创建结果显示界面
    createResultInterface(result_panel);
end

function createParameterInterface(parent)
    %% 创建参数设置界面
    
    % 获取默认参数
    params = getDefaultParameters();
    
    % 创建滚动面板
    scroll_panel = uipanel('Parent', parent, ...
                          'Position', [0.02, 0.02, 0.96, 0.96], ...
                          'BorderType', 'none');
    
    y_pos = 0.95;  % 起始Y位置
    y_step = 0.08; % Y步长
    
    %% 1. 介质物理参数组
    y_pos = createParameterGroup(scroll_panel, '1. 介质物理参数', y_pos, y_step);
    
    % 井孔介质参数
    y_pos = createSubGroup(scroll_panel, '井孔介质（泥浆）', y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'po1', '密度 (kg/m³)', params.po1, [500, 2000], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'vp1', '纵波速度 (m/s)', params.vp1, [1000, 2000], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'vs1', '横波速度 (m/s)', params.vs1, [0, 100], y_pos, y_step);
    
    % 地层介质参数
    y_pos = createSubGroup(scroll_panel, '地层介质（围岩）', y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'po2', '密度 (kg/m³)', params.po2, [1500, 3000], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'vp2', '纵波速度 (m/s)', params.vp2, [2000, 6000], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'vs2', '横波速度 (m/s)', params.vs2, [1000, 4000], y_pos, y_step);
    
    %% 2. 震源参数组
    y_pos = createParameterGroup(scroll_panel, '2. 震源参数', y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'f0', '主频率 (Hz)', params.f0, [5000, 20000], y_pos, y_step);
    
    %% 3. 几何参数组
    y_pos = createParameterGroup(scroll_panel, '3. 几何参数', y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'cal', '井径 (m)', params.cal, [0.05, 0.3], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'L_StoR', '源距 (m)', params.L_StoR, [0.5, 3.0], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'L_RtoR', '接收器间距 (m)', params.L_RtoR, [0.1, 0.3], y_pos, y_step);
    
    %% 4. 计算参数组
    y_pos = createParameterGroup(scroll_panel, '4. 计算参数', y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'num_s', '总炮数', params.num_s, [10, 100], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'shot_start', '起始炮号', params.shot_start, [1, 67], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'shot_end', '结束炮号', params.shot_end, [1, 67], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'maxt', '时间步数', params.maxt, [1000, 5000], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'N', '检波器数量', params.N, [10, 30], y_pos, y_step);
    
    %% 5. 网格参数组
    y_pos = createParameterGroup(scroll_panel, '5. 网格参数', y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'pml', 'PML边界厚度', params.pml, [20, 100], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'nx', 'X方向网格数', params.nx, [200, 500], y_pos, y_step);
    y_pos = createParameterControl(scroll_panel, 'nz', 'Z方向网格数', params.nz, [800, 2000], y_pos, y_step);
    
    %% 6. 高级选项组
    y_pos = createParameterGroup(scroll_panel, '6. 高级选项', y_pos, y_step);
    
    % 索引模式选择
    uicontrol('Parent', scroll_panel, ...
              'Style', 'text', ...
              'String', '数据索引模式:', ...
              'Position', [20, (1-y_pos)*400, 120, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 10);
    
    uicontrol('Parent', scroll_panel, ...
              'Style', 'checkbox', ...
              'String', '直接索引 (推荐)', ...
              'Position', [150, (1-y_pos)*400, 150, 20], ...
              'Value', 1, ...
              'Tag', 'use_direct_indexing', ...
              'FontSize', 10);
    
    y_pos = y_pos - y_step;
    
    %% 控制按钮
    y_pos = y_pos - y_step;
    
    % 运行仿真按钮
    uicontrol('Parent', scroll_panel, ...
              'Style', 'pushbutton', ...
              'String', '开始仿真', ...
              'Position', [20, (1-y_pos)*400, 100, 35], ...
              'FontSize', 12, ...
              'FontWeight', 'bold', ...
              'BackgroundColor', [0.2, 0.7, 0.2], ...
              'ForegroundColor', 'white', ...
              'Callback', @runSimulation);
    
    % 重置参数按钮
    uicontrol('Parent', scroll_panel, ...
              'Style', 'pushbutton', ...
              'String', '重置参数', ...
              'Position', [130, (1-y_pos)*400, 100, 35], ...
              'FontSize', 12, ...
              'Callback', @resetParameters);
    
    % 保存参数按钮
    uicontrol('Parent', scroll_panel, ...
              'Style', 'pushbutton', ...
              'String', '保存参数', ...
              'Position', [240, (1-y_pos)*400, 100, 35], ...
              'FontSize', 12, ...
              'Callback', @saveParameters);
    
    % 加载参数按钮
    uicontrol('Parent', scroll_panel, ...
              'Style', 'pushbutton', ...
              'String', '加载参数', ...
              'Position', [350, (1-y_pos)*400, 100, 35], ...
              'FontSize', 12, ...
              'Callback', @loadParameters);
end

function createResultInterface(parent)
    %% 创建结果显示界面
    
    % 创建选项卡组
    tabgroup = uitabgroup('Parent', parent, ...
                         'Position', [0.02, 0.02, 0.96, 0.96]);
    
    % 实时监控选项卡
    monitor_tab = uitab(tabgroup, 'Title', '实时监控');
    createMonitorInterface(monitor_tab);
    
    % 结果分析选项卡
    analysis_tab = uitab(tabgroup, 'Title', '结果分析');
    createAnalysisInterface(analysis_tab);
    
    % 参数对比选项卡
    compare_tab = uitab(tabgroup, 'Title', '参数对比');
    createCompareInterface(compare_tab);
end

function createMonitorInterface(parent)
    %% 创建实时监控界面
    
    % 进度显示
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', '仿真进度:', ...
              'Position', [20, 350, 80, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 10);
    
    progress_bar = uicontrol('Parent', parent, ...
                            'Style', 'text', ...
                            'String', '0%', ...
                            'Position', [110, 350, 300, 20], ...
                            'BackgroundColor', [0.9, 0.9, 0.9], ...
                            'Tag', 'progress_bar');
    
    % 状态显示
    status_text = uicontrol('Parent', parent, ...
                           'Style', 'text', ...
                           'String', '就绪', ...
                           'Position', [20, 320, 400, 20], ...
                           'HorizontalAlignment', 'left', ...
                           'FontSize', 10, ...
                           'Tag', 'status_text');
    
    % 波场显示区域
    axes('Parent', parent, ...
         'Position', [0.05, 0.05, 0.9, 0.7], ...
         'Tag', 'wavefield_axes');
    
    title('波场快照 (实时更新)');
    xlabel('X方向 (网格点)');
    ylabel('Z方向 (网格点)');
end

function createAnalysisInterface(parent)
    %% 创建结果分析界面

    % 数据选择控件
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', '选择数据文件:', ...
              'Position', [20, 350, 100, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 10);

    file_popup = uicontrol('Parent', parent, ...
                          'Style', 'popupmenu', ...
                          'String', {'选择文件...'}, ...
                          'Position', [130, 350, 200, 25], ...
                          'Tag', 'file_popup', ...
                          'Callback', @updateFileList);

    % 刷新文件列表按钮
    uicontrol('Parent', parent, ...
              'Style', 'pushbutton', ...
              'String', '刷新', ...
              'Position', [340, 350, 60, 25], ...
              'Callback', @refreshFileList);

    % 炮号选择
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', '炮号:', ...
              'Position', [20, 320, 50, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 10);

    shot_slider = uicontrol('Parent', parent, ...
                           'Style', 'slider', ...
                           'Position', [80, 320, 200, 20], ...
                           'Min', 1, 'Max', 67, 'Value', 1, ...
                           'Tag', 'shot_slider', ...
                           'Callback', @updateShotDisplay);

    shot_edit = uicontrol('Parent', parent, ...
                         'Style', 'edit', ...
                         'String', '1', ...
                         'Position', [290, 320, 50, 20], ...
                         'Tag', 'shot_edit', ...
                         'Callback', @updateShotSlider);

    % 显示模式选择
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', '显示模式:', ...
              'Position', [20, 290, 80, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 10);

    display_popup = uicontrol('Parent', parent, ...
                             'Style', 'popupmenu', ...
                             'String', {'波形显示', '密度图', '3D显示', '频谱分析'}, ...
                             'Position', [110, 290, 120, 25], ...
                             'Tag', 'display_popup', ...
                             'Callback', @updateDisplayMode);

    % 结果显示区域
    axes('Parent', parent, ...
         'Position', [0.05, 0.05, 0.9, 0.65], ...
         'Tag', 'result_axes');

    title('分析结果显示');
    xlabel('时间/频率');
    ylabel('幅度');
end

function createCompareInterface(parent)
    %% 创建参数对比界面

    % 对比模式选择
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', '对比模式:', ...
              'Position', [20, 350, 80, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 10);

    compare_popup = uicontrol('Parent', parent, ...
                             'Style', 'popupmenu', ...
                             'String', {'频率对比', '介质参数对比', '几何参数对比', '自定义对比'}, ...
                             'Position', [110, 350, 150, 25], ...
                             'Tag', 'compare_popup', ...
                             'Callback', @updateCompareMode);

    % 参数范围设置
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', '参数范围:', ...
              'Position', [20, 320, 80, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 10);

    % 最小值
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', '最小值:', ...
              'Position', [110, 320, 50, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 9);

    min_edit = uicontrol('Parent', parent, ...
                        'Style', 'edit', ...
                        'String', '5000', ...
                        'Position', [165, 320, 60, 20], ...
                        'Tag', 'min_edit');

    % 最大值
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', '最大值:', ...
              'Position', [235, 320, 50, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 9);

    max_edit = uicontrol('Parent', parent, ...
                        'Style', 'edit', ...
                        'String', '15000', ...
                        'Position', [290, 320, 60, 20], ...
                        'Tag', 'max_edit');

    % 步数
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', '步数:', ...
              'Position', [360, 320, 40, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 9);

    steps_edit = uicontrol('Parent', parent, ...
                          'Style', 'edit', ...
                          'String', '5', ...
                          'Position', [405, 320, 40, 20], ...
                          'Tag', 'steps_edit');

    % 开始对比按钮
    uicontrol('Parent', parent, ...
              'Style', 'pushbutton', ...
              'String', '开始对比', ...
              'Position', [20, 290, 100, 25], ...
              'FontSize', 10, ...
              'BackgroundColor', [0.2, 0.5, 0.8], ...
              'ForegroundColor', 'white', ...
              'Callback', @startComparison);

    % 对比结果显示区域
    axes('Parent', parent, ...
         'Position', [0.05, 0.05, 0.9, 0.65], ...
         'Tag', 'compare_axes');

    title('参数对比结果');
    xlabel('参数值');
    ylabel('响应特征');
end

%% 辅助函数

function y_pos = createParameterGroup(parent, title, y_pos, y_step)
    %% 创建参数组标题
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', title, ...
              'Position', [10, (1-y_pos)*400, 400, 25], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 11, ...
              'FontWeight', 'bold', ...
              'BackgroundColor', [0.8, 0.9, 1.0]);
    y_pos = y_pos - y_step;
end

function y_pos = createSubGroup(parent, title, y_pos, y_step)
    %% 创建子组标题
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', ['  ' title], ...
              'Position', [20, (1-y_pos)*400, 350, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 10, ...
              'FontWeight', 'bold', ...
              'BackgroundColor', [0.9, 0.95, 1.0]);
    y_pos = y_pos - y_step * 0.7;
end

function y_pos = createParameterControl(parent, tag, label, default_val, range, y_pos, y_step)
    %% 创建参数控制组件

    % 标签
    uicontrol('Parent', parent, ...
              'Style', 'text', ...
              'String', ['    ' label ':'], ...
              'Position', [30, (1-y_pos)*400, 120, 20], ...
              'HorizontalAlignment', 'left', ...
              'FontSize', 9);

    % 滑块
    slider = uicontrol('Parent', parent, ...
                      'Style', 'slider', ...
                      'Position', [160, (1-y_pos)*400, 150, 20], ...
                      'Min', range(1), 'Max', range(2), ...
                      'Value', default_val, ...
                      'Tag', [tag '_slider'], ...
                      'Callback', {@updateParameterFromSlider, tag});

    % 编辑框
    edit_box = uicontrol('Parent', parent, ...
                        'Style', 'edit', ...
                        'String', num2str(default_val), ...
                        'Position', [320, (1-y_pos)*400, 80, 20], ...
                        'Tag', [tag '_edit'], ...
                        'Callback', {@updateParameterFromEdit, tag});

    % 存储控件句柄
    setappdata(slider, 'edit_handle', edit_box);
    setappdata(edit_box, 'slider_handle', slider);

    y_pos = y_pos - y_step * 0.8;
end

function params = getDefaultParameters()
    %% 获取默认参数值
    params.po1 = 1000;      % 井孔密度
    params.vp1 = 1500;      % 井孔纵波速度
    params.vs1 = 0;         % 井孔横波速度
    params.po2 = 2300;      % 地层密度
    params.vp2 = 4500;      % 地层纵波速度
    params.vs2 = 2300;      % 地层横波速度
    params.f0 = 10000;      % 震源频率
    params.cal = 0.1;       % 井径
    params.L_StoR = 1.5;    % 源距
    params.L_RtoR = 0.15;   % 接收器间距
    params.num_s = 67;      % 总炮数
    params.shot_start = 1;  % 起始炮号
    params.shot_end = 67;   % 结束炮号
    params.maxt = 2000;     % 时间步数
    params.N = 21;          % 检波器数量
    params.pml = 50;        % PML厚度
    params.nx = 300;        % X方向网格数
    params.nz = 1300;       % Z方向网格数
end

%% 回调函数

function updateParameterFromSlider(src, ~, tag)
    %% 从滑块更新参数值
    value = get(src, 'Value');
    edit_handle = getappdata(src, 'edit_handle');

    % 根据参数类型格式化显示
    if contains(tag, {'num_s', 'shot_start', 'shot_end', 'maxt', 'N', 'pml', 'nx', 'nz'})
        % 整数参数
        value = round(value);
        set(edit_handle, 'String', num2str(value));
    else
        % 浮点参数
        set(edit_handle, 'String', sprintf('%.1f', value));
    end

    % 更新滑块值
    set(src, 'Value', value);

    % 实时参数验证
    validateParameters();
end

function updateParameterFromEdit(src, ~, tag)
    %% 从编辑框更新参数值
    value_str = get(src, 'String');
    value = str2double(value_str);

    if isnan(value)
        % 输入无效，恢复原值
        slider_handle = getappdata(src, 'slider_handle');
        old_value = get(slider_handle, 'Value');
        set(src, 'String', num2str(old_value));
        return;
    end

    slider_handle = getappdata(src, 'slider_handle');
    slider_min = get(slider_handle, 'Min');
    slider_max = get(slider_handle, 'Max');

    % 限制在滑块范围内
    value = max(slider_min, min(slider_max, value));

    % 整数参数处理
    if contains(tag, {'num_s', 'shot_start', 'shot_end', 'maxt', 'N', 'pml', 'nx', 'nz'})
        value = round(value);
    end

    set(slider_handle, 'Value', value);
    set(src, 'String', num2str(value));

    % 实时参数验证
    validateParameters();
end

function validateParameters()
    %% 参数验证和相互约束检查

    % 获取当前参数值
    shot_start = getParameterValue('shot_start');
    shot_end = getParameterValue('shot_end');
    num_s = getParameterValue('num_s');

    % 炮数范围检查
    if shot_start > shot_end
        % 自动调整
        setParameterValue('shot_end', shot_start);
    end

    if shot_end > num_s
        setParameterValue('shot_end', num_s);
    end

    % 更新状态显示
    updateStatusDisplay();
end

function value = getParameterValue(tag)
    %% 获取参数值
    edit_handle = findobj('Tag', [tag '_edit']);
    if ~isempty(edit_handle)
        value = str2double(get(edit_handle, 'String'));
    else
        value = 0;
    end
end

function setParameterValue(tag, value)
    %% 设置参数值
    edit_handle = findobj('Tag', [tag '_edit']);
    slider_handle = findobj('Tag', [tag '_slider']);

    if ~isempty(edit_handle)
        set(edit_handle, 'String', num2str(value));
    end

    if ~isempty(slider_handle)
        set(slider_handle, 'Value', value);
    end
end

function updateStatusDisplay()
    %% 更新状态显示
    status_text = findobj('Tag', 'status_text');
    if ~isempty(status_text)
        shot_start = getParameterValue('shot_start');
        shot_end = getParameterValue('shot_end');
        actual_shots = shot_end - shot_start + 1;

        status_str = sprintf('就绪 - 将运行第%d~%d炮，共%d炮', ...
                           shot_start, shot_end, actual_shots);
        set(status_text, 'String', status_str);
    end
end

function runSimulation(~, ~)
    %% 运行仿真

    % 更新状态
    status_text = findobj('Tag', 'status_text');
    progress_bar = findobj('Tag', 'progress_bar');

    set(status_text, 'String', '正在准备仿真参数...');
    set(progress_bar, 'String', '0%', 'BackgroundColor', [1, 1, 0]);
    drawnow;

    try
        % 收集所有参数
        params = collectAllParameters();

        % 生成参数文件
        generateParameterFile(params);

        set(status_text, 'String', '正在运行FDTD仿真...');
        set(progress_bar, 'String', '10%');
        drawnow;

        % 运行仿真
        runFDTDSimulation();

        set(status_text, 'String', '仿真完成！');
        set(progress_bar, 'String', '100%', 'BackgroundColor', [0, 1, 0]);

        % 自动刷新结果文件列表
        refreshFileList();

        % 显示完成消息
        msgbox('FDTD仿真已完成！请在"结果分析"选项卡中查看结果。', ...
               '仿真完成', 'help');

    catch ME
        set(status_text, 'String', ['错误: ' ME.message]);
        set(progress_bar, 'String', '错误', 'BackgroundColor', [1, 0, 0]);

        errordlg(['仿真过程中发生错误: ' ME.message], '仿真错误');
    end
end

function params = collectAllParameters()
    %% 收集所有参数
    param_names = {'po1', 'vp1', 'vs1', 'po2', 'vp2', 'vs2', 'f0', 'cal', ...
                   'L_StoR', 'L_RtoR', 'num_s', 'shot_start', 'shot_end', ...
                   'maxt', 'N', 'pml', 'nx', 'nz'};

    params = struct();
    for i = 1:length(param_names)
        params.(param_names{i}) = getParameterValue(param_names{i});
    end

    % 获取索引模式
    checkbox = findobj('Tag', 'use_direct_indexing');
    if ~isempty(checkbox)
        params.use_direct_indexing = get(checkbox, 'Value');
    else
        params.use_direct_indexing = 1;
    end
end

function generateParameterFile(params)
    %% 生成参数文件

    % 创建临时参数文件
    filename = 'GUI_generated_params.m';
    fid = fopen(filename, 'w');

    if fid == -1
        error('无法创建参数文件');
    end

    try
        % 写入参数
        fprintf(fid, '%% GUI生成的参数文件\n');
        fprintf(fid, '%% 生成时间: %s\n\n', char(datetime('now')));

        % 介质参数
        fprintf(fid, '%% 介质物理参数\n');
        fprintf(fid, 'po1 = %.1f;  %% 井孔密度 [kg/m^3]\n', params.po1);
        fprintf(fid, 'vp1 = %.1f;  %% 井孔纵波速度 [m/s]\n', params.vp1);
        fprintf(fid, 'vs1 = %.1f;  %% 井孔横波速度 [m/s]\n', params.vs1);
        fprintf(fid, 'po2 = %.1f;  %% 地层密度 [kg/m^3]\n', params.po2);
        fprintf(fid, 'vp2 = %.1f;  %% 地层纵波速度 [m/s]\n', params.vp2);
        fprintf(fid, 'vs2 = %.1f;  %% 地层横波速度 [m/s]\n', params.vs2);

        % 震源参数
        fprintf(fid, '\n%% 震源参数\n');
        fprintf(fid, 'f0 = %.0f;   %% 主频率 [Hz]\n', params.f0);

        % 几何参数
        fprintf(fid, '\n%% 几何参数\n');
        fprintf(fid, 'cal = %.3f;     %% 井径 [m]\n', params.cal);
        fprintf(fid, 'L_StoR = %.2f;  %% 源距 [m]\n', params.L_StoR);
        fprintf(fid, 'L_RtoR = %.3f;  %% 接收器间距 [m]\n', params.L_RtoR);

        % 计算参数
        fprintf(fid, '\n%% 计算参数\n');
        fprintf(fid, 'num_s = %d;        %% 总炮数\n', params.num_s);
        fprintf(fid, 'shot_start = %d;   %% 起始炮号\n', params.shot_start);
        fprintf(fid, 'shot_end = %d;     %% 结束炮号\n', params.shot_end);
        fprintf(fid, 'maxt = %d;         %% 时间步数\n', params.maxt);
        fprintf(fid, 'N = %d;            %% 检波器数量\n', params.N);

        % 网格参数
        fprintf(fid, '\n%% 网格参数\n');
        fprintf(fid, 'pml = %d;    %% PML边界厚度\n', params.pml);
        fprintf(fid, 'nx = %d;     %% X方向网格数\n', params.nx);
        fprintf(fid, 'nz = %d;     %% Z方向网格数\n', params.nz);

        % 高级选项
        fprintf(fid, '\n%% 高级选项\n');
        if params.use_direct_indexing
            fprintf(fid, 'use_direct_indexing = true;  %% 使用直接索引\n');
        else
            fprintf(fid, 'use_direct_indexing = false; %% 使用相对索引\n');
        end

        fclose(fid);

    catch ME
        fclose(fid);
        rethrow(ME);
    end
end

function runFDTDSimulation()
    %% 运行FDTD仿真

    % 修改主程序中的参数
    modifyMainProgram();

    % 运行主程序
    evalin('base', 'kuaisu_main');
end

function modifyMainProgram()
    %% 修改主程序参数

    % 读取GUI生成的参数
    if exist('GUI_generated_params.m', 'file')
        run('GUI_generated_params.m');

        % 将参数传递到基础工作空间
        param_names = {'po1', 'vp1', 'vs1', 'po2', 'vp2', 'vs2', 'f0', 'cal', ...
                       'L_StoR', 'L_RtoR', 'num_s', 'shot_start', 'shot_end', ...
                       'maxt', 'N', 'pml', 'nx', 'nz', 'use_direct_indexing'};

        for i = 1:length(param_names)
            if exist(param_names{i}, 'var')
                assignin('base', param_names{i}, eval(param_names{i}));
            end
        end
    end
end

function resetParameters(~, ~)
    %% 重置参数到默认值

    answer = questdlg('确定要重置所有参数到默认值吗？', ...
                     '重置参数', 'Yes', 'No', 'No');

    if strcmp(answer, 'Yes')
        params = getDefaultParameters();
        param_names = fieldnames(params);

        for i = 1:length(param_names)
            setParameterValue(param_names{i}, params.(param_names{i}));
        end

        % 重置索引模式
        checkbox = findobj('Tag', 'use_direct_indexing');
        if ~isempty(checkbox)
            set(checkbox, 'Value', 1);
        end

        updateStatusDisplay();
        msgbox('参数已重置到默认值', '重置完成', 'help');
    end
end

function saveParameters(~, ~)
    %% 保存参数到文件

    [filename, pathname] = uiputfile('*.mat', '保存参数文件', 'FDTD_params.mat');

    if filename ~= 0
        params = collectAllParameters();
        save(fullfile(pathname, filename), 'params');
        msgbox(['参数已保存到: ' fullfile(pathname, filename)], '保存成功', 'help');
    end
end

function loadParameters(~, ~)
    %% 从文件加载参数

    [filename, pathname] = uigetfile('*.mat', '选择参数文件');

    if filename ~= 0
        try
            data = load(fullfile(pathname, filename));

            if isfield(data, 'params')
                params = data.params;
                param_names = fieldnames(params);

                for i = 1:length(param_names)
                    if strcmp(param_names{i}, 'use_direct_indexing')
                        checkbox = findobj('Tag', 'use_direct_indexing');
                        if ~isempty(checkbox)
                            set(checkbox, 'Value', params.(param_names{i}));
                        end
                    else
                        setParameterValue(param_names{i}, params.(param_names{i}));
                    end
                end

                updateStatusDisplay();
                msgbox(['参数已从文件加载: ' filename], '加载成功', 'help');
            else
                errordlg('文件格式不正确，缺少params结构体', '加载错误');
            end

        catch ME
            errordlg(['加载参数文件时发生错误: ' ME.message], '加载错误');
        end
    end
end

function refreshFileList(~, ~)
    %% 刷新结果文件列表

    % 查找FDTD结果文件
    files = dir('FDTD_SeismicLogging_*.mat');

    file_popup = findobj('Tag', 'file_popup');
    if ~isempty(file_popup)
        if isempty(files)
            set(file_popup, 'String', {'无结果文件'});
        else
            file_names = {files.name};
            % 按时间排序，最新的在前
            [~, idx] = sort([files.datenum], 'descend');
            file_names = file_names(idx);
            set(file_popup, 'String', [{'选择文件...'}, file_names]);
        end
    end
end

function updateFileList(src, ~)
    %% 更新文件选择

    selected_idx = get(src, 'Value');
    file_list = get(src, 'String');

    if selected_idx > 1 && selected_idx <= length(file_list)
        selected_file = file_list{selected_idx};

        % 加载文件并更新显示
        try
            data = load(selected_file);

            % 更新炮号滑块范围
            if isfield(data, 'shot_start') && isfield(data, 'shot_end')
                shot_slider = findobj('Tag', 'shot_slider');
                shot_edit = findobj('Tag', 'shot_edit');

                if ~isempty(shot_slider)
                    set(shot_slider, 'Min', data.shot_start, 'Max', data.shot_end, ...
                        'Value', data.shot_start);
                end

                if ~isempty(shot_edit)
                    set(shot_edit, 'String', num2str(data.shot_start));
                end
            end

            % 更新结果显示
            updateResultDisplay(selected_file);

        catch ME
            errordlg(['加载结果文件时发生错误: ' ME.message], '文件加载错误');
        end
    end
end

function updateShotDisplay(src, ~)
    %% 更新炮号显示

    shot_num = round(get(src, 'Value'));
    shot_edit = findobj('Tag', 'shot_edit');

    if ~isempty(shot_edit)
        set(shot_edit, 'String', num2str(shot_num));
    end

    % 更新结果显示
    updateCurrentShotDisplay(shot_num);
end

function updateShotSlider(src, ~)
    %% 从编辑框更新炮号滑块

    shot_num = str2double(get(src, 'String'));
    shot_slider = findobj('Tag', 'shot_slider');

    if ~isempty(shot_slider) && ~isnan(shot_num)
        slider_min = get(shot_slider, 'Min');
        slider_max = get(shot_slider, 'Max');

        shot_num = max(slider_min, min(slider_max, round(shot_num)));
        set(shot_slider, 'Value', shot_num);
        set(src, 'String', num2str(shot_num));

        % 更新结果显示
        updateCurrentShotDisplay(shot_num);
    end
end

function updateDisplayMode(~, ~)
    %% 更新显示模式
    updateCurrentShotDisplay();
end

function updateCurrentShotDisplay(shot_num)
    %% 更新当前炮的显示

    if nargin < 1
        shot_edit = findobj('Tag', 'shot_edit');
        if ~isempty(shot_edit)
            shot_num = str2double(get(shot_edit, 'String'));
        else
            shot_num = 1;
        end
    end

    % 获取当前选择的文件
    file_popup = findobj('Tag', 'file_popup');
    if isempty(file_popup)
        return;
    end

    selected_idx = get(file_popup, 'Value');
    file_list = get(file_popup, 'String');

    if selected_idx <= 1 || selected_idx > length(file_list)
        return;
    end

    selected_file = file_list{selected_idx};

    try
        % 加载数据
        data_struct = load(selected_file);

        % 提取指定炮的数据
        if isfield(data_struct, 'use_direct_indexing') && data_struct.use_direct_indexing
            % 直接索引模式
            if shot_num <= size(data_struct.data, 1)
                shot_data_1d = data_struct.data(shot_num, :);
            else
                return;
            end
        else
            % 相对索引模式
            if isfield(data_struct, 'shot_start')
                matrix_row = shot_num - data_struct.shot_start + 1;
                if matrix_row > 0 && matrix_row <= size(data_struct.data, 1)
                    shot_data_1d = data_struct.data(matrix_row, :);
                else
                    return;
                end
            else
                return;
            end
        end

        % 重组数据
        if isfield(data_struct, 'N') && isfield(data_struct, 'maxt')
            shot_data = reshape(shot_data_1d, data_struct.maxt, data_struct.N)';
        else
            return;
        end

        % 获取显示模式
        display_popup = findobj('Tag', 'display_popup');
        if ~isempty(display_popup)
            display_mode = get(display_popup, 'Value');
        else
            display_mode = 1;
        end

        % 显示结果
        result_axes = findobj('Tag', 'result_axes');
        if ~isempty(result_axes)
            axes(result_axes);
            cla;

            switch display_mode
                case 1  % 波形显示
                    plot(shot_data(1,:), 'b-', 'LineWidth', 1.5);
                    hold on;
                    if size(shot_data, 1) > 1
                        plot(shot_data(end,:), 'r--', 'LineWidth', 1.5);
                        legend('第1道', sprintf('第%d道', size(shot_data, 1)));
                    end
                    title(sprintf('第%d炮波形显示', shot_num));
                    xlabel('时间采样点');
                    ylabel('幅度');
                    grid on;

                case 2  % 密度图
                    imagesc(shot_data);
                    colorbar;
                    title(sprintf('第%d炮密度图', shot_num));
                    xlabel('时间采样点');
                    ylabel('道号');

                case 3  % 3D显示
                    [X, Y] = meshgrid(1:size(shot_data, 2), 1:size(shot_data, 1));
                    surf(X, Y, shot_data);
                    shading interp;
                    colorbar;
                    title(sprintf('第%d炮3D显示', shot_num));
                    xlabel('时间采样点');
                    ylabel('道号');
                    zlabel('幅度');

                case 4  % 频谱分析
                    % 对第一道进行FFT
                    signal = shot_data(1, :);
                    N_fft = length(signal);
                    f = (0:N_fft-1) * (1/data_struct.dt) / N_fft;
                    Y = fft(signal);

                    plot(f(1:N_fft/2), abs(Y(1:N_fft/2)));
                    title(sprintf('第%d炮第1道频谱分析', shot_num));
                    xlabel('频率 (Hz)');
                    ylabel('幅度');
                    grid on;
            end
        end

    catch ME
        % 显示错误信息
        result_axes = findobj('Tag', 'result_axes');
        if ~isempty(result_axes)
            axes(result_axes);
            cla;
            text(0.5, 0.5, ['显示错误: ' ME.message], ...
                 'HorizontalAlignment', 'center', ...
                 'VerticalAlignment', 'middle', ...
                 'FontSize', 12, 'Color', 'red');
        end
    end
end

function updateResultDisplay(filename)
    %% 更新结果显示

    try
        data_struct = load(filename);

        % 显示基本信息
        result_axes = findobj('Tag', 'result_axes');
        if ~isempty(result_axes)
            axes(result_axes);
            cla;

            % 显示数据概览
            if isfield(data_struct, 'data')
                imagesc(data_struct.data);
                colorbar;
                title(['数据概览: ' filename], 'Interpreter', 'none');
                xlabel('时间采样点 (所有道连续排列)');

                if isfield(data_struct, 'use_direct_indexing') && data_struct.use_direct_indexing
                    ylabel('炮号 (直接索引)');
                else
                    ylabel('矩阵行号 (相对索引)');
                end
            end
        end

    catch ME
        result_axes = findobj('Tag', 'result_axes');
        if ~isempty(result_axes)
            axes(result_axes);
            cla;
            text(0.5, 0.5, ['加载错误: ' ME.message], ...
                 'HorizontalAlignment', 'center', ...
                 'VerticalAlignment', 'middle', ...
                 'FontSize', 12, 'Color', 'red');
        end
    end
end

function updateCompareMode(~, ~)
    %% 更新对比模式
    % 这里可以添加对比模式的具体实现
    compare_axes = findobj('Tag', 'compare_axes');
    if ~isempty(compare_axes)
        axes(compare_axes);
        cla;
        text(0.5, 0.5, '对比功能开发中...', ...
             'HorizontalAlignment', 'center', ...
             'VerticalAlignment', 'middle', ...
             'FontSize', 14);
    end
end

function startComparison(~, ~)
    %% 开始参数对比
    msgbox('参数对比功能正在开发中，敬请期待！', '功能提示', 'help');
end
