# 二维FDTD声波测井仿真系统 - 图形界面

## 系统概述

本系统为二维FDTD声波测井正演模拟程序提供了专业的图形化用户界面，特别适用于：
- **专利申请技术演示**
- **参数优化研究**
- **教学和培训**
- **工程应用**

## 主要特性

### 🎛️ 参数控制
- **实时参数调节**：滑块和数值输入双重控制
- **参数分组管理**：按功能分类的清晰界面
- **参数验证**：自动检查参数合理性和相互约束
- **参数保存/加载**：支持参数配置的保存和复用

### 📊 结果可视化
- **多种显示模式**：波形、密度图、3D显示、频谱分析
- **实时结果更新**：参数改变后立即看到效果
- **炮号选择**：灵活选择查看特定炮的结果
- **数据对比**：不同参数组合的对比分析

### 🔧 专业功能
- **直接炮号索引**：解决了原程序的索引问题
- **进度监控**：实时显示仿真进度
- **错误处理**：友好的错误提示和恢复机制
- **批量处理**：支持参数扫描和批量计算

## 安装和启动

### 系统要求
- MATLAB R2018a 或更高版本
- 推荐内存：4GB 以上
- 硬盘空间：至少1GB可用空间

### 启动方法

**方法1：使用启动脚本（推荐）**
```matlab
run('start_FDTD_GUI.m')
```

**方法2：直接启动**
```matlab
FDTD_GUI_Main()
```

## 界面布局

### 左侧面板：参数设置
```
┌─────────────────────────────┐
│ 1. 介质物理参数              │
│   ├─ 井孔介质（泥浆）        │
│   │   ├─ 密度 (kg/m³)       │
│   │   ├─ 纵波速度 (m/s)     │
│   │   └─ 横波速度 (m/s)     │
│   └─ 地层介质（围岩）        │
│       ├─ 密度 (kg/m³)       │
│       ├─ 纵波速度 (m/s)     │
│       └─ 横波速度 (m/s)     │
│                             │
│ 2. 震源参数                 │
│   └─ 主频率 (Hz)            │
│                             │
│ 3. 几何参数                 │
│   ├─ 井径 (m)               │
│   ├─ 源距 (m)               │
│   └─ 接收器间距 (m)         │
│                             │
│ 4. 计算参数                 │
│   ├─ 总炮数                 │
│   ├─ 起始炮号               │
│   ├─ 结束炮号               │
│   ├─ 时间步数               │
│   └─ 检波器数量             │
│                             │
│ 5. 网格参数                 │
│   ├─ PML边界厚度            │
│   ├─ X方向网格数            │
│   └─ Z方向网格数            │
│                             │
│ 6. 高级选项                 │
│   └─ 数据索引模式           │
│                             │
│ [开始仿真] [重置] [保存] [加载] │
└─────────────────────────────┘
```

### 右侧面板：结果显示
```
┌─────────────────────────────┐
│ [实时监控] [结果分析] [参数对比] │
├─────────────────────────────┤
│                             │
│     结果显示区域             │
│                             │
│   ┌─────────────────────┐   │
│   │                     │   │
│   │    图表/波形显示     │   │
│   │                     │   │
│   └─────────────────────┘   │
│                             │
│ 控制选项：                   │
│ ├─ 文件选择                 │
│ ├─ 炮号选择                 │
│ ├─ 显示模式                 │
│ └─ 分析选项                 │
└─────────────────────────────┘
```

## 使用流程

### 1. 基本仿真流程
```mermaid
graph TD
    A[启动GUI] --> B[设置参数]
    B --> C[开始仿真]
    C --> D[监控进度]
    D --> E[查看结果]
    E --> F{满意结果?}
    F -->|否| B
    F -->|是| G[保存结果]
```

### 2. 参数优化流程
```mermaid
graph TD
    A[确定优化目标] --> B[设置基准参数]
    B --> C[选择变化参数]
    C --> D[设置参数范围]
    D --> E[批量仿真]
    E --> F[结果对比]
    F --> G[分析最优参数]
```

## 参数说明

### 介质物理参数
| 参数 | 符号 | 单位 | 典型范围 | 说明 |
|------|------|------|----------|------|
| 井孔密度 | po1 | kg/m³ | 500-2000 | 泥浆密度 |
| 井孔纵波速度 | vp1 | m/s | 1000-2000 | 泥浆中的声速 |
| 井孔横波速度 | vs1 | m/s | 0-100 | 通常为0（流体） |
| 地层密度 | po2 | kg/m³ | 1500-3000 | 围岩密度 |
| 地层纵波速度 | vp2 | m/s | 2000-6000 | 围岩纵波速度 |
| 地层横波速度 | vs2 | m/s | 1000-4000 | 围岩横波速度 |

### 震源参数
| 参数 | 符号 | 单位 | 典型范围 | 说明 |
|------|------|------|----------|------|
| 主频率 | f0 | Hz | 5000-20000 | Ricker子波中心频率 |

### 几何参数
| 参数 | 符号 | 单位 | 典型范围 | 说明 |
|------|------|------|----------|------|
| 井径 | cal | m | 0.05-0.3 | 井孔直径 |
| 源距 | L_StoR | m | 0.5-3.0 | 震源到第一个接收器距离 |
| 接收器间距 | L_RtoR | m | 0.1-0.3 | 相邻接收器间距 |

### 计算参数
| 参数 | 符号 | 单位 | 典型范围 | 说明 |
|------|------|------|----------|------|
| 总炮数 | num_s | - | 10-100 | 仪器总长度对应的炮数 |
| 起始炮号 | shot_start | - | 1-num_s | 开始计算的炮号 |
| 结束炮号 | shot_end | - | shot_start-num_s | 结束计算的炮号 |
| 时间步数 | maxt | - | 1000-5000 | 时间采样点数 |
| 检波器数量 | N | - | 10-30 | 接收器阵列道数 |

## 专利申请建议

### 技术亮点展示
1. **参数实时调节**：展示不同参数对声波传播的影响
2. **直观结果显示**：多种可视化方式展现技术效果
3. **参数优化过程**：记录参数调节和优化的完整过程
4. **技术对比分析**：与传统方法的对比优势

### 关键截图建议
1. **界面总览**：展示完整的GUI界面
2. **参数调节过程**：展示参数滑块调节的动态效果
3. **结果对比图**：不同参数下的波形对比
4. **3D可视化**：立体展示声波传播特征
5. **频谱分析**：展示频域分析能力

### 技术文档准备
1. **参数影响分析**：每个参数对结果的具体影响
2. **优化案例**：典型地层条件下的参数优化实例
3. **精度验证**：与理论解或实验数据的对比
4. **计算效率**：不同参数设置下的计算性能

## 故障排除

### 常见问题

**Q1: GUI启动失败**
- 检查MATLAB版本（推荐R2018a+）
- 确保所有必要文件在当前目录
- 重启MATLAB后重试

**Q2: 仿真运行错误**
- 检查参数设置是否合理
- 确保内存充足
- 查看MATLAB命令窗口的详细错误信息

**Q3: 结果显示异常**
- 刷新结果文件列表
- 检查数据文件是否完整
- 尝试重新加载数据文件

**Q4: 参数调节无响应**
- 检查参数是否在有效范围内
- 重置参数到默认值后重试
- 检查参数相互约束关系

### 性能优化建议
1. **内存管理**：大网格计算时适当减少时间步数
2. **计算效率**：调试时使用较少的炮数
3. **显示优化**：大数据量时选择合适的显示模式
4. **文件管理**：定期清理旧的结果文件

## 技术支持

如需技术支持，请提供：
1. MATLAB版本信息
2. 错误信息截图
3. 参数设置详情
4. 系统配置信息

---

**版本信息**
- 当前版本：1.0
- 更新日期：2025年
- 兼容性：MATLAB R2018a+
